(()=>{"use strict";var e={n:t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return e.d(o,{a:o}),o},d:(t,o)=>{for(var r in o)e.o(o,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:o[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{__experimentalDeRegisterExpressPaymentMethod:()=>P,__experimentalDeRegisterPaymentMethod:()=>k,__experimentalRegisterProductCollection:()=>z,getExpressPaymentMethods:()=>S,getPaymentMethods:()=>I,getRegisteredBlockComponents:()=>A,getRegisteredInnerBlocks:()=>T,registerBlockComponent:()=>R,registerExpressPaymentMethod:()=>g,registerInnerBlock:()=>O,registerPaymentMethod:()=>h,registerPaymentMethodExtensionCallbacks:()=>v});const o=window.wp.deprecated;var r=e.n(o);const n=window.wp.data,a={},i=a,s=(e,t,o)=>{const r=((e,t)=>o=>(o?.paymentRequirements||[]).every((e=>t.includes(e)))&&e(o))(e,t);return Object.values(i).some((e=>o in e))?((e,t,o)=>r=>{let n=e(r);if(n){const e={};Object.entries(t).forEach((([t,r])=>{o in r&&"function"==typeof r[o]&&(e[t]=r[o])})),n=Object.keys(e).every((t=>{try{return e[t](r)}catch(e){return console.error(`Error when executing callback for ${o} in ${t}`,e),!0}}))}return n})(r,i,o):r},c=window.wp.element,l=(e,t)=>{if(null!==e&&!(0,c.isValidElement)(e))throw new TypeError(`The ${t} property for the payment method must be a React element or null.`)},u=(e,t=[])=>{const o=t.reduce(((t,o)=>(e.hasOwnProperty(o)||t.push(o),t)),[]);if(o.length>0)throw new TypeError("The payment method configuration object is missing the following properties:"+o.join(", "))},m=window.ReactJSXRuntime,p=()=>null;class d{constructor(e){d.assertValidConfig(e),this.name=e.name,this.label=e.label,this.placeOrderButtonLabel=e.placeOrderButtonLabel,this.ariaLabel=e.ariaLabel,this.content=e.content,this.savedTokenComponent=e.savedTokenComponent,this.icons=e.icons||null,this.edit=e.edit,this.paymentMethodId=e.paymentMethodId||this.name,this.supports={showSavedCards:e?.supports?.showSavedCards||e?.supports?.savePaymentInfo||!1,showSaveOption:e?.supports?.showSaveOption||!1,features:e?.supports?.features||["products"]},this.canMakePaymentFromConfig=e.canMakePayment}get canMakePayment(){return s(this.canMakePaymentFromConfig,this.supports.features,this.name)}static assertValidConfig=e=>{if(e.savedTokenComponent=e.savedTokenComponent||(0,m.jsx)(p,{}),u(e,["name","label","ariaLabel","content","edit","canMakePayment"]),"string"!=typeof e.name)throw new Error("The name property for the payment method must be a string");if(void 0!==e.icons&&!Array.isArray(e.icons)&&null!==e.icons)throw new Error("The icons property for the payment method must be an array or null.");if("string"!=typeof e.paymentMethodId&&void 0!==e.paymentMethodId)throw new Error("The paymentMethodId property for the payment method must be a string or undefined (in which case it will be the value of the name property).");if("string"!=typeof e.placeOrderButtonLabel&&void 0!==e.placeOrderButtonLabel)throw new TypeError("The placeOrderButtonLabel property for the payment method must be a string");if((e=>{if(null!==e&&!(0,c.isValidElement)(e)&&"string"!=typeof e)throw new TypeError("The label property for the payment method must be a React element, a string, or null.")})(e.label),l(e.content,"content"),l(e.edit,"edit"),l(e.savedTokenComponent,"savedTokenComponent"),"string"!=typeof e.ariaLabel)throw new TypeError("The ariaLabel property for the payment method must be a string");if("function"!=typeof e.canMakePayment)throw new TypeError("The canMakePayment property for the payment method must be a function.");if(void 0!==e.supports?.showSavedCards&&"boolean"!=typeof e.supports?.showSavedCards)throw new TypeError("If the payment method includes the `supports.showSavedCards` property, it must be a boolean");if(void 0!==e.supports?.savePaymentInfo&&r()("Passing savePaymentInfo when registering a payment method.",{alternative:"Pass showSavedCards and showSaveOption",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/3686"}),void 0!==e.supports?.features&&!Array.isArray(e.supports?.features))throw new Error("The features property for the payment method must be an array or undefined.");if(void 0!==e.supports?.showSaveOption&&"boolean"!=typeof e.supports?.showSaveOption)throw new TypeError("If the payment method includes the `supports.showSaveOption` property, it must be a boolean")}}class y{constructor(e){const t="string"==typeof e.name?e.name.replace(/[_-]/g," "):e.name,o="string"==typeof e?.description&&e.description.length>130?e.description.slice(0,130)+"...":e.description;y.assertValidConfig(e),this.name=e.name,this.title=e.title||t,this.description=o||"",this.gatewayId=e.gatewayId||"",this.content=e.content,this.edit=e.edit,this.paymentMethodId=e.paymentMethodId||this.name,this.supports={features:e?.supports?.features||["products"],style:e?.supports?.style||[]},this.canMakePaymentFromConfig=e.canMakePayment}get canMakePayment(){return s(this.canMakePaymentFromConfig,this.supports.features,this.name)}static assertValidConfig=e=>{if(u(e,["name","content","edit"]),"string"!=typeof e.name)throw new TypeError("The name property for the express payment method must be a string");if("string"!=typeof e.paymentMethodId&&void 0!==e.paymentMethodId)throw new Error("The paymentMethodId property for the payment method must be a string or undefined (in which case it will be the value of the name property).");if(void 0!==e.supports?.features&&!Array.isArray(e.supports?.features))throw new Error("The features property for the payment method must be an array or undefined.");if(l(e.content,"content"),l(e.edit,"edit"),"function"!=typeof e.canMakePayment)throw new TypeError("The canMakePayment property for the express payment method must be a function.")}}const b="wc/store/payment",w={},f={},h=e=>{let t;"function"==typeof e?(t=e(d),r()("Passing a callback to registerPaymentMethod()",{alternative:"a config options object",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/3404"})):t=new d(e),t instanceof d&&(w[t.name]=t)},g=e=>{let t;"function"==typeof e?(t=e(y),r()("Passing a callback to registerExpressPaymentMethod()",{alternative:"a config options object",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/3404"})):t=new y(e),t instanceof y&&(f[t.name]=t)},v=(e,t)=>{a[e]?console.error(`The namespace provided to registerPaymentMethodExtensionCallbacks must be unique. Callbacks have already been registered for the ${e} namespace.`):(a[e]={},Object.entries(t).forEach((([t,o])=>{"function"==typeof o?a[e][t]=o:console.error(`All callbacks provided to registerPaymentMethodExtensionCallbacks must be functions. The callback for the ${t} payment method in the ${e} namespace was not a function.`)})))},k=e=>{delete w[e];const{__internalRemoveAvailablePaymentMethod:t}=(0,n.dispatch)(b);t(e)},P=e=>{delete f[e];const{__internalRemoveAvailableExpressPaymentMethod:t}=(0,n.dispatch)(b);t(e)},I=()=>w,S=()=>f,E={};function A(e){return{..."object"==typeof E[e]&&Object.keys(E[e]).length>0?E[e]:{},...E.any}}function T(e){return r()("getRegisteredInnerBlocks",{version:"2.8.0",alternative:"getRegisteredBlockComponents",plugin:"WooCommerce Blocks"}),A(e)}const C=(e,t,o)=>{const r=typeof e[t];if(r!==o)throw new Error(`Incorrect value for the ${t} argument when registering a block component. It was a ${r}, but must be a ${o}.`)},x=(e,t)=>{if(e[t]){if("function"==typeof e[t])return;if(e[t].$$typeof&&e[t].$$typeof===Symbol.for("react.lazy"))return}throw new Error(`Incorrect value for the ${t} argument when registering a block component. Component must be a valid React Element or Lazy callback.`)};function R(e){e.context||(e.context="any"),C(e,"context","string"),C(e,"blockName","string"),x(e,"component");const{context:t,blockName:o,component:r}=e;E[t]||(E[t]={}),E[t][o]=r}function O(e){r()("registerInnerBlock",{version:"2.8.0",alternative:"registerBlockComponent",plugin:"WooCommerce Blocks",hint:'"main" has been replaced with "context" and is now optional.'}),C(e,"main","string"),R({...e,context:e.main})}const q=window.wp.hooks;let M=function(e){return e.GRID="flex",e.STACK="list",e}({}),j=function(e){return e.FILL="fill",e.FIXED="fixed",e}({}),B=function(e){return e.ATTRIBUTES="attributes",e.CREATED="created",e.FEATURED="featured",e.HAND_PICKED="hand-picked",e.INHERIT="inherit",e.KEYWORD="keyword",e.ON_SALE="on-sale",e.ORDER="order",e.DEFAULT_ORDER="default-order",e.STOCK_STATUS="stock-status",e.TAXONOMY="taxonomy",e.PRICE_RANGE="price-range",e.FILTERABLE="filterable",e.PRODUCTS_PER_PAGE="products-per-page",e.MAX_PAGES_TO_SHOW="max-pages-to-show",e.OFFSET="offset",e.RELATED_BY="related-by",e}({});const L=window.wc.wcSettings,_=JSON.parse('{"UU":"woocommerce/product-collection"}');let F=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({});const D=_.UU,$=`${D}/product-title`,H=(0,L.getSetting)("stockStatusOptions",[]),N={perPage:9,pages:0,offset:0,postType:"product",order:"asc",orderBy:"title",search:"",exclude:[],inherit:!1,taxQuery:{},isProductCollectionBlock:!0,featured:!1,woocommerceOnSale:!1,woocommerceStockStatus:(0,L.getSetting)("hideOutOfStockItems",!1)?Object.keys(function(e,t){const{[t]:o,...r}=e;return r}(H,"outofstock")):Object.keys(H),woocommerceAttributes:[],woocommerceHandPickedProducts:[],timeFrame:void 0,priceRange:void 0,filterable:!1,relatedBy:{categories:!0,tags:!0}},U={query:N,tagName:"div",displayLayout:{type:M.GRID,columns:3,shrinkColumns:!0},dimensions:{widthType:j.FILL},queryContextIncludes:["collection"],forcePageReload:!1},V=[["woocommerce/product-template",{},[["woocommerce/product-image",{imageSizing:F.THUMBNAIL}],["core/post-title",{textAlign:"center",level:2,fontSize:"medium",style:{spacing:{margin:{bottom:"0.75rem",top:"0"}},typography:{lineHeight:"1.4"}},isLink:!0,__woocommerceNamespace:$}],["woocommerce/product-price",{textAlign:"center",fontSize:"small"}],["woocommerce/product-button",{textAlign:"center",fontSize:"small"}]]],["core/query-pagination",{layout:{type:"flex",justifyContent:"center"}}],["woocommerce/product-collection-no-results"]],z=e=>{if(!(e=>"object"!=typeof e||null===e?(console.error("Invalid arguments: You must pass an object to __experimentalRegisterProductCollection."),!1):"string"!=typeof e.name||0===e.name.length?(console.error("Invalid name: name must be a non-empty string."),!1):(e.name.match(/^[a-zA-Z0-9-]+\/product-collection\/[a-zA-Z0-9-]+$/)||console.warn('To prevent conflicts with other collections, please use a unique name following the pattern: "<plugin-name>/product-collection/<collection-name>". Ensure "<plugin-name>" is your plugin name and "<collection-name>" is your collection name. Both should consist only of alphanumeric characters and hyphens (e.g., "my-plugin/product-collection/my-collection").'),"string"!=typeof e.title||0===e.title.length?(console.error("Invalid title: title must be a non-empty string."),!1):(void 0!==e.description&&"string"!=typeof e.description&&console.warn("Invalid description: description must be a string."),void 0!==e.category&&"string"!=typeof e.category&&console.warn("Invalid category: category must be a string."),void 0===e.keywords||Array.isArray(e.keywords)||console.warn("Invalid keywords: keywords must be an array of strings."),void 0!==e.icon&&"string"!=typeof e.icon&&"object"!=typeof e.icon&&console.warn("Invalid icon: icon must be a string or an object."),void 0!==e.example&&"object"!=typeof e.example&&console.warn("Invalid example: example must be an object."),void 0===e.scope||Array.isArray(e.scope)||console.warn("Invalid scope: scope must be an array of type WPBlockVariationScope."),void 0!==e.attributes&&"object"!=typeof e.attributes&&console.warn("Invalid attributes: attributes must be an object."),void 0!==e.attributes?.query&&"object"!=typeof e.attributes.query&&console.warn("Invalid query: query must be an object."),void 0!==e.attributes?.query?.offset&&"number"!=typeof e.attributes.query.offset&&console.warn("Invalid offset: offset must be a number."),void 0!==e.attributes?.query?.order&&"string"!=typeof e.attributes.query.order&&console.warn("Invalid order: order must be a string."),void 0!==e.attributes?.query?.orderBy&&"string"!=typeof e.attributes.query.orderBy&&console.warn("Invalid orderBy: orderBy must be a string."),void 0!==e.attributes?.query?.pages&&"number"!=typeof e.attributes.query.pages&&console.warn("Invalid pages: pages must be a number."),void 0!==e.attributes?.query?.perPage&&"number"!=typeof e.attributes.query.perPage&&console.warn("Invalid perPage: perPage must be a number."),void 0!==e.attributes?.query?.search&&"string"!=typeof e.attributes.query.search&&console.warn("Invalid search: search must be a string."),void 0!==e.attributes?.query?.taxQuery&&"object"!=typeof e.attributes.query.taxQuery&&console.warn("Invalid taxQuery: taxQuery must be an object."),void 0!==e.attributes?.query?.featured&&"boolean"!=typeof e.attributes.query.featured&&console.warn("Invalid featured: featured must be a boolean."),void 0!==e.attributes?.query?.timeFrame&&"object"!=typeof e.attributes.query.timeFrame&&console.warn("Invalid timeFrame: timeFrame must be an object."),void 0!==e.attributes?.query?.woocommerceOnSale&&"boolean"!=typeof e.attributes.query.woocommerceOnSale&&console.warn("Invalid woocommerceOnSale: woocommerceOnSale must be a boolean."),void 0===e.attributes?.query?.woocommerceStockStatus||Array.isArray(e.attributes.query.woocommerceStockStatus)||console.warn("Invalid woocommerceStockStatus: woocommerceStockStatus must be an array."),void 0===e.attributes?.query?.woocommerceAttributes||Array.isArray(e.attributes.query.woocommerceAttributes)||console.warn("Invalid woocommerceAttributes: woocommerceAttributes must be an array."),void 0===e.attributes?.query?.woocommerceHandPickedProducts||Array.isArray(e.attributes.query.woocommerceHandPickedProducts)||console.warn("Invalid woocommerceHandPickedProducts: woocommerceHandPickedProducts must be an array."),void 0!==e.attributes?.query?.priceRange&&"object"!=typeof e.attributes.query.priceRange&&console.warn("Invalid priceRange: priceRange must be an object."),void 0!==e.attributes?.displayLayout&&"object"!=typeof e.attributes.displayLayout&&console.warn("Invalid displayLayout: displayLayout must be an object."),void 0!==e.attributes?.dimensions&&"object"!=typeof e.attributes.dimensions&&console.warn("Invalid dimensions: dimensions must be an object."),void 0===e.attributes?.hideControls||Array.isArray(e.attributes.hideControls)||console.warn("Invalid hideControls: hideControls must be an array of strings."),void 0===e.attributes?.queryContextIncludes||Array.isArray(e.attributes.queryContextIncludes)||console.warn("Invalid queryContextIncludes: queryContextIncludes must be an array of strings."),void 0!==e.preview&&("object"==typeof e.preview&&null!==e.preview||console.warn("Invalid preview: preview must be an object."),void 0!==e.preview.setPreviewState&&"function"!=typeof e.preview.setPreviewState&&console.warn("Invalid preview: setPreviewState must be a function."),void 0!==e.preview.initialPreviewState&&("object"!=typeof e.preview.initialPreviewState&&console.warn("Invalid preview: initialPreviewState must be an object."),"boolean"!=typeof e.preview.initialPreviewState.isPreview&&console.warn("Invalid preview: preview.isPreview must be a boolean."),"string"!=typeof e.preview.initialPreviewState.previewMessage&&console.warn("Invalid preview: preview.previewMessage must be a string."))),!(void 0!==e.usesReference&&!Array.isArray(e.usesReference)&&(console.error("Invalid usesReference: usesReference must be an array of strings."),1)))))(e))return void console.error("Collection could not be registered due to invalid configuration.");const{preview:{setPreviewState:t,initialPreviewState:o}={},usesReference:r}=e,n=e.attributes?.query||{},a=[...new Set([B.INHERIT,...e.attributes?.hideControls||[]])],i={name:e.name,title:e.title,description:e.description,category:e.category,keywords:e.keywords,icon:e.icon,example:e.example,scope:e.scope,attributes:{...e.attributes,query:{...N,...void 0!==n.offset&&{offset:n.offset},...void 0!==n.order&&{order:n.order},...void 0!==n.orderBy&&{orderBy:n.orderBy},...void 0!==n.pages&&{pages:n.pages},...void 0!==n.perPage&&{perPage:n.perPage},...void 0!==n.search&&{search:n.search},...void 0!==n.taxQuery&&{taxQuery:n.taxQuery},...void 0!==n.featured&&{featured:n.featured},...void 0!==n.timeFrame&&{timeFrame:n.timeFrame},...void 0!==n.woocommerceOnSale&&{woocommerceOnSale:n.woocommerceOnSale},...void 0!==n.woocommerceStockStatus&&{woocommerceStockStatus:n.woocommerceStockStatus},...void 0!==n.woocommerceAttributes&&{woocommerceAttributes:n.woocommerceAttributes},...void 0!==n.woocommerceHandPickedProducts&&{woocommerceHandPickedProducts:n.woocommerceHandPickedProducts},...void 0!==n.priceRange&&{priceRange:n.priceRange}},hideControls:a,collection:e.name,inherit:!1},innerBlocks:e.innerBlocks||V,isActive:(e,t)=>e.collection===t.collection,isDefault:!1};if(t||o||Array.isArray(r)&&r.length>0){const e=e=>n=>n.attributes.collection!==i.name?(0,m.jsx)(e,{...n}):(0,m.jsx)(e,{...n,...o||t?{preview:{setPreviewState:t,initialPreviewState:o}}:{},usesReference:r});(0,q.addFilter)("editor.BlockEdit",i.name,e)}wp?.blocks?.registerBlockVariation&&wp.blocks.registerBlockVariation(D,{...i,attributes:{...U,...i.attributes,query:{...N,...i.attributes?.query},displayLayout:{...U.displayLayout,...i.attributes?.displayLayout}}})};(this.wc=this.wc||{}).wcBlocksRegistry=t})()
;