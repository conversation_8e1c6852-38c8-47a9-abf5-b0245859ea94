(()=>{"use strict";var e={d:(t,r)=>{for(var o in r)e.o(r,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:r[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ADMIN_URL:()=>R,COUNTRIES:()=>v,CURRENCY:()=>W,CURRENT_USER_IS_ADMIN:()=>T,HOME_URL:()=>x,LOCALE:()=>O,ORDER_STATUSES:()=>L,PLACEHOLDER_IMG_SRC:()=>P,SITE_CURRENCY:()=>j,SITE_TITLE:()=>C,STORE_PAGES:()=>A,WC_ASSET_URL:()=>M,WC_VERSION:()=>N,WP_LOGIN_URL:()=>V,WP_VERSION:()=>D,allSettings:()=>n,defaultFields:()=>k,getAdminLink:()=>U,getCurrencyPrefix:()=>E,getCurrencySuffix:()=>I,getPaymentMethodData:()=>b,getSetting:()=>f,getSettingWithCoercion:()=>y,isWcVersion:()=>h,isWpVersion:()=>w}),(0,window.wp.hooks.addFilter)("woocommerce_admin_analytics_settings","woocommerce-blocks/exclude-draft-status-from-analytics",(e=>{const t=e=>"customStatuses"===e.key?{...e,options:e.options.filter((e=>"checkout-draft"!==e.value))}:e,r=e.woocommerce_actionable_order_statuses.options.map(t),o=e.woocommerce_excluded_report_order_statuses.options.map(t);return{...e,woocommerce_actionable_order_statuses:{...e.woocommerce_actionable_order_statuses,options:r},woocommerce_excluded_report_order_statuses:{...e.woocommerce_excluded_report_order_statuses,options:o}}}));const r={adminUrl:"",countries:[],countryData:{},currency:{code:"USD",precision:2,symbol:"$",symbolPosition:"left",decimalSeparator:".",priceFormat:"%1$s%2$s",thousandSeparator:","},currentUserId:0,currentUserIsAdmin:!1,homeUrl:"",locale:{siteLocale:"en_US",userLocale:"en_US",weekdaysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},orderStatuses:[],placeholderImgSrc:"",siteTitle:"",storePages:[],wcAssetUrl:"",wcVersion:"",wpLoginUrl:"",wpVersion:""},o="object"==typeof window.wcSettings?window.wcSettings:{},n={...r,...o};function s(e,t){const r=c(e),o=c(t),n=r.pop(),s=o.pop(),i=p(r,o);return 0!==i?i:n&&s?p(n.split("."),s.split(".")):n||s?n?-1:1:0}n.currency={...r.currency,...n.currency},n.locale={...r.locale,...n.locale};const i=(e,t,r)=>{S(r);const o=s(e,t);return m[r].includes(o)};s.validate=e=>"string"==typeof e&&/^[v\d]/.test(e)&&a.test(e),s.compare=i,s.sastisfies=(e,t)=>{const r=t.match(/^([<>=~^]+)/),o=r?r[1]:"=";if("^"!==o&&"~"!==o)return i(e,t,o);const[n,s,a]=c(e),[l,d,m]=c(t);return 0===u(n,l)&&("^"===o?p([s,a],[d,m])>=0:0===u(s,d)&&u(a,m)>=0)};const a=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,c=e=>{if("string"!=typeof e)throw new TypeError("Invalid argument expected string");const t=e.match(a);if(!t)throw new Error(`Invalid argument not valid semver ('${e}' received)`);return t.shift(),t},l=e=>"*"===e||"x"===e||"X"===e,d=e=>{const t=parseInt(e,10);return isNaN(t)?e:t},u=(e,t)=>{if(l(e)||l(t))return 0;const[r,o]=((e,t)=>typeof e!=typeof t?[String(e),String(t)]:[e,t])(d(e),d(t));return r>o?1:r<o?-1:0},p=(e,t)=>{for(let r=0;r<Math.max(e.length,t.length);r++){const o=u(e[r]||0,t[r]||0);if(0!==o)return o}return 0},m={">":[1],">=":[0,1],"=":[0],"<=":[-1,0],"<":[-1]},_=Object.keys(m),S=e=>{if("string"!=typeof e)throw new TypeError("Invalid operator type, expected string but got "+typeof e);if(-1===_.indexOf(e))throw new Error(`Invalid operator, expected one of ${_.join("|")}`)},f=(e,t=!1,r=(e,t)=>void 0!==e?e:t)=>{let o=t;if(e in n)o=n[e];else if(e.includes("_data")){const r=e.replace("_data",""),n=f("paymentMethodData",{});o=r in n?n[r]:t}return r(o,t)},y=(e,t,r)=>{const o=e in n?n[e]:t;return r(o,t)?o:t},g=(e,t,r)=>{let o=f(e,"").replace(/-[a-zA-Z0-9]*[\-]*/,".0-rc.");return o=o.endsWith(".")?o.substring(0,o.length-1):o,s.compare(o,t,r)},w=(e,t="=")=>g("wpVersion",e,t),h=(e,t="=")=>g("wcVersion",e,t),U=e=>f("adminUrl")+e,b=(e,t=null)=>{var r;return null!==(r=f("paymentMethodData",{})[e])&&void 0!==r?r:t},E=(e,t)=>({left:e,left_space:e+" ",right:"",right_space:""}[t]||""),I=(e,t)=>({left:"",left_space:"",right:e,right_space:" "+e}[t]||""),R=n.adminUrl,v=n.countries,T=n.currentUserIsAdmin,x=n.homeUrl,O=n.locale,L=n.orderStatuses,P=n.placeholderImgSrc,C=n.siteTitle,A=n.storePages,M=n.wcAssetUrl,N=n.wcVersion,V=n.wpLoginUrl,D=n.wpVersion,W=n.currency,j={code:W.code,symbol:W.symbol,thousandSeparator:W.thousandSeparator,decimalSeparator:W.decimalSeparator,minorUnit:W.precision,prefix:E(W.symbol,W.symbolPosition),suffix:I(W.symbol,W.symbolPosition)},k=f("defaultFields");(this.wc=this.wc||{}).wcSettings=t})()
;