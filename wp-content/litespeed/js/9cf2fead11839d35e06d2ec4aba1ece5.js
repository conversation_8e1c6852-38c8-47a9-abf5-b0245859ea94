/*! This file is auto-generated */
(()=>{"use strict";var e={d:(r,t)=>{for(var n in t)e.o(t,n)&&!e.o(r,n)&&Object.defineProperty(r,n,{enumerable:!0,get:t[n]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r)},r={};e.d(r,{default:()=>T});const t=window.wp.i18n;const n=function(e){const r=(e,t)=>{const{headers:n={}}=e;for(const o in n)if("x-wp-nonce"===o.toLowerCase()&&n[o]===r.nonce)return t(e);return t({...e,headers:{...n,"X-WP-Nonce":r.nonce}})};return r.nonce=e,r},o=(e,r)=>{let t,n,o=e.path;return"string"==typeof e.namespace&&"string"==typeof e.endpoint&&(t=e.namespace.replace(/^\/|\/$/g,""),n=e.endpoint.replace(/^\//,""),o=n?t+"/"+n:t),delete e.namespace,delete e.endpoint,r({...e,path:o})},a=e=>(r,t)=>o(r,(r=>{let n,o=r.url,a=r.path;return"string"==typeof a&&(n=e,-1!==e.indexOf("?")&&(a=a.replace("?","&")),a=a.replace(/^\//,""),"string"==typeof n&&-1!==n.indexOf("?")&&(a=a.replace("?","&")),o=n+a),t({...r,url:o})})),s=window.wp.url;function i(e,r){if(r)return Promise.resolve(e.body);try{return Promise.resolve(new window.Response(JSON.stringify(e.body),{status:200,statusText:"OK",headers:e.headers}))}catch{return Object.entries(e.headers).forEach((([r,t])=>{"link"===r.toLowerCase()&&(e.headers[r]=t.replace(/<([^>]+)>/,((e,r)=>`<${encodeURI(r)}>`)))})),Promise.resolve(r?e.body:new window.Response(JSON.stringify(e.body),{status:200,statusText:"OK",headers:e.headers}))}}const c=function(e){const r=Object.fromEntries(Object.entries(e).map((([e,r])=>[(0,s.normalizePath)(e),r])));return(e,t)=>{const{parse:n=!0}=e;let o=e.path;if(!o&&e.url){const{rest_route:r,...t}=(0,s.getQueryArgs)(e.url);"string"==typeof r&&(o=(0,s.addQueryArgs)(r,t))}if("string"!=typeof o)return t(e);const a=e.method||"GET",c=(0,s.normalizePath)(o);if("GET"===a&&r[c]){const e=r[c];return delete r[c],i(e,!!n)}if("OPTIONS"===a&&r[a]&&r[a][c]){const e=r[a][c];return delete r[a][c],i(e,!!n)}return t(e)}},d=({path:e,url:r,...t},n)=>({...t,url:r&&(0,s.addQueryArgs)(r,n),path:e&&(0,s.addQueryArgs)(e,n)}),p=e=>e.json?e.json():Promise.reject(e),u=e=>{const{next:r}=(e=>{if(!e)return{};const r=e.match(/<([^>]+)>; rel="next"/);return r?{next:r[1]}:{}})(e.headers.get("link"));return r},h=async(e,r)=>{if(!1===e.parse)return r(e);if(!(e=>{const r=!!e.path&&-1!==e.path.indexOf("per_page=-1"),t=!!e.url&&-1!==e.url.indexOf("per_page=-1");return r||t})(e))return r(e);const t=await T({...d(e,{per_page:100}),parse:!1}),n=await p(t);if(!Array.isArray(n))return n;let o=u(t);if(!o)return n;let a=[].concat(n);for(;o;){const r=await T({...e,path:void 0,url:o,parse:!1}),t=await p(r);a=a.concat(t),o=u(r)}return a},l=new Set(["PATCH","PUT","DELETE"]),w="GET",f=(e,r=!0)=>Promise.resolve(((e,r=!0)=>r?204===e.status?null:e.json?e.json():Promise.reject(e):e)(e,r)).catch((e=>m(e,r)));function m(e,r=!0){if(!r)throw e;return(e=>{const r={code:"invalid_json",message:(0,t.__)("The response is not a valid JSON response.")};if(!e||!e.json)throw r;return e.json().catch((()=>{throw r}))})(e).then((e=>{const r={code:"unknown_error",message:(0,t.__)("An unknown error occurred.")};throw e||r}))}const g=(e,r)=>{if(!function(e){const r=!!e.method&&"POST"===e.method;return(!!e.path&&-1!==e.path.indexOf("/wp/v2/media")||!!e.url&&-1!==e.url.indexOf("/wp/v2/media"))&&r}(e))return r(e);let n=0;const o=e=>(n++,r({path:`/wp/v2/media/${e}/post-process`,method:"POST",data:{action:"create-image-subsizes"},parse:!1}).catch((()=>n<5?o(e):(r({path:`/wp/v2/media/${e}?force=true`,method:"DELETE"}),Promise.reject()))));return r({...e,parse:!1}).catch((r=>{if(!r.headers)return Promise.reject(r);const n=r.headers.get("x-wp-upload-attachment-id");return r.status>=500&&r.status<600&&n?o(n).catch((()=>!1!==e.parse?Promise.reject({code:"post_process",message:(0,t.__)("Media upload failed. If this is a photo or a large image, please scale it down and try again.")}):Promise.reject(r))):m(r,e.parse)})).then((r=>f(r,e.parse)))},y=e=>(r,t)=>{if("string"==typeof r.url){const t=(0,s.getQueryArg)(r.url,"wp_theme_preview");void 0===t?r.url=(0,s.addQueryArgs)(r.url,{wp_theme_preview:e}):""===t&&(r.url=(0,s.removeQueryArgs)(r.url,"wp_theme_preview"))}if("string"==typeof r.path){const t=(0,s.getQueryArg)(r.path,"wp_theme_preview");void 0===t?r.path=(0,s.addQueryArgs)(r.path,{wp_theme_preview:e}):""===t&&(r.path=(0,s.removeQueryArgs)(r.path,"wp_theme_preview"))}return t(r)},_={Accept:"application/json, */*;q=0.1"},v={credentials:"include"},P=[(e,r)=>("string"!=typeof e.url||(0,s.hasQueryArg)(e.url,"_locale")||(e.url=(0,s.addQueryArgs)(e.url,{_locale:"user"})),"string"!=typeof e.path||(0,s.hasQueryArg)(e.path,"_locale")||(e.path=(0,s.addQueryArgs)(e.path,{_locale:"user"})),r(e)),o,(e,r)=>{const{method:t=w}=e;return l.has(t.toUpperCase())&&(e={...e,headers:{...e.headers,"X-HTTP-Method-Override":t,"Content-Type":"application/json"},method:"POST"}),r(e)},h];const O=e=>{if(e.status>=200&&e.status<300)return e;throw e};let j=e=>{const{url:r,path:n,data:o,parse:a=!0,...s}=e;let{body:i,headers:c}=e;c={..._,...c},o&&(i=JSON.stringify(o),c["Content-Type"]="application/json");return window.fetch(r||n||window.location.href,{...v,...s,body:i,headers:c}).then((e=>Promise.resolve(e).then(O).catch((e=>m(e,a))).then((e=>f(e,a)))),(e=>{if(e&&"AbortError"===e.name)throw e;throw{code:"fetch_error",message:(0,t.__)("You are probably offline.")}}))};function A(e){return P.reduceRight(((e,r)=>t=>r(t,e)),j)(e).catch((r=>"rest_cookie_invalid_nonce"!==r.code?Promise.reject(r):window.fetch(A.nonceEndpoint).then(O).then((e=>e.text())).then((r=>(A.nonceMiddleware.nonce=r,A(e))))))}A.use=function(e){P.unshift(e)},A.setFetchHandler=function(e){j=e},A.createNonceMiddleware=n,A.createPreloadingMiddleware=c,A.createRootURLMiddleware=a,A.fetchAllMiddleware=h,A.mediaUploadMiddleware=g,A.createThemePreviewMiddleware=y;const T=A;(window.wp=window.wp||{}).apiFetch=r.default})();
;