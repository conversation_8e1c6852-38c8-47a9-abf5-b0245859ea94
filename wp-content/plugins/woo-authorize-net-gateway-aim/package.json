{"name": "woo-authorize-net-gateway-aim", "version": "6.1.17", "author": "Pledged Plugins", "license": "GPL-3.0+", "main": "build/index.js", "scripts": {"build": "wp-scripts build", "format": "wp-scripts format", "lint:css": "wp-scripts lint-style", "lint:js": "wp-scripts lint-js", "packages-update": "wp-scripts packages-update", "plugin-zip": "wp-scripts plugin-zip", "start": "wp-scripts start", "postinstall": "composer install"}, "dependencies": {"@wordpress/hooks": "^4.18.0", "@wordpress/i18n": "^5.18.0", "react-payment-inputs": "^1.2.0", "styled-components": "^6.1.15"}, "devDependencies": {"@woocommerce/dependency-extraction-webpack-plugin": "^3.1.0", "@woocommerce/eslint-plugin": "^2.3.0", "@wordpress/prettier-config": "^4.18.0", "@wordpress/scripts": "^28.6.0"}, "files": ["/assets", "/build", "/freemius", "/includes", "/languages", "/woo-includes", "/updates", "gateway.php", "block.json", "readme.txt", "readme.md", "README.md"]}