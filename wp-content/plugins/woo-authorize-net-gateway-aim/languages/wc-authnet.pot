#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: WooCommerce Authorize.Net Gateway\n"
"POT-Creation-Date: 2025-05-30 15:12+0530\n"
"PO-Revision-Date: 2025-05-30 15:12+0530\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: gateway.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: node_modules\n"

#: gateway.php:152
msgid "About Authorize.Net"
msgstr ""

#: gateway.php:153
#, php-format
msgid ""
"As a leading payment gateway, %sAuthorize.Net%s is trusted by more than "
"430,000 merchants, handling more than 1 billion transactions and $149 "
"billion in payments every year. Authorize.Net has been working with "
"merchants and small businesses since 1996 and will offer you a credit card "
"payment solution that works for your business and lets you focus on what you "
"love best."
msgstr ""

#: gateway.php:154
msgid "About this WooCommerce Extension"
msgstr ""

#: gateway.php:155
msgid ""
"This extension enables you to use the Authorize.Net payment gateway to "
"accept payments via credit cards directly on checkout on your WooCommerce "
"powered WordPress e-commerce website without redirecting customers away to "
"the gateway website."
msgstr ""

#: gateway.php:158 gateway.php:206
msgid "Settings"
msgstr ""

#: gateway.php:161 gateway.php:207
msgid "Support"
msgstr ""

#: gateway.php:164
msgid "License"
msgstr ""

#: gateway.php:165
#, php-format
msgid ""
"You are using our %1$sFREE PRO%2$s version of the extension. Here are the "
"features you will get access to if you upgrade to the %1$sENTERPRISE%2$s "
"version:"
msgstr ""

#: gateway.php:167
msgid "Process Subscriptions:"
msgstr ""

#: gateway.php:168
#, php-format
msgid ""
"Use with %1$sWooCommerce Subscriptions%2$s extension to %3$screate and "
"manage products with recurring payments%4$s — payments that will give you "
"residual revenue you can track and count on."
msgstr ""

#: gateway.php:170
msgid "Setup Pre-Orders:"
msgstr ""

#: gateway.php:171
#, php-format
msgid ""
"Use with %1$sWooCommerce Pre-Orders%2$s extension so customers can order "
"products before they’re available by submitting their card details. The card "
"is then automatically charged when the pre-order is available."
msgstr ""

#: gateway.php:173
msgid "Pay via Saved Cards:"
msgstr ""

#: gateway.php:174
msgid ""
"Enable option to use saved card details on the gateway servers for quicker "
"checkout. No sensitive card data is stored on the website!"
msgstr ""

#: gateway.php:176
msgid "ACH Payments:"
msgstr ""

#: gateway.php:177
msgid "Fully supports eCheck payments via ACH network."
msgstr ""

#: gateway.php:179
msgid "One Click Upsells:"
msgstr ""

#: gateway.php:180
#, php-format
msgid ""
"Compatible with %1$sFunnelKit (formerly WooFunnels) One Click Upsells%2$s."
msgstr ""

#: gateway.php:183
msgid "Upgrade to Enterprise!"
msgstr ""

#: gateway.php:208
msgid "About"
msgstr ""

#: gateway.php:302
#, php-format
msgid ""
"Authorize.Net is almost ready. To get started, <a href=\"%s\">set your "
"Authorize.Net account keys</a>."
msgstr ""

#: gateway.php:348
#, php-format
msgid ""
"WooCommerce Authorize.Net - The minimum PHP version required for this plugin "
"is %1$s. You are running %2$s."
msgstr ""

#: gateway.php:354
msgid "WooCommerce Authorize.Net requires WooCommerce to be activated to work."
msgstr ""

#: gateway.php:358
#, php-format
msgid ""
"WooCommerce Authorize.Net - The minimum WooCommerce version required for "
"this plugin is %1$s. You are running %2$s."
msgstr ""

#: gateway.php:364
msgid "WooCommerce Authorize.Net - cURL is not installed."
msgstr ""

#: gateway.php:387
msgid ""
"To process subscription payments using Authorize.Net you will need the <a "
"target=\"_blank\" href=\"https://woocommerce.com/products/woocommerce-"
"subscriptions/\">WooCommerce Subscriptions</a> extension installed and "
"running. Please continue with your purchase if you are not setting up "
"subscriptions or will install WooCommerce Subscriptions later."
msgstr ""

#: gateway.php:393
msgid "<h3>WooCommerce Subscriptions Not Detected!</h3>"
msgstr ""

#: gateway.php:485 gateway.php:602
#, php-format
msgid ""
"<strong>Unable to capture charge!</strong> Please <strong>DO NOT FULFIL THE "
"ORDER</strong> if the amount cannot be captured in the gateway account "
"manually or by changing the status. In that case, set status to Failed "
"manually and do not fulfil. \n"
"\n"
"Authorize.Net failure reason: %s \n"
"\n"
msgstr ""

#: gateway.php:487 gateway.php:604
#, php-format
msgid ""
"<strong>Unable to capture charge!</strong> The order status is set to "
"<strong>Failed</strong> the first time to draw your attention. If the next "
"attempt fails, your intended order status will still take place. \n"
"\n"
"Please double-check that the amount is captured in the gateway account "
"before fulfilling the order. \n"
"\n"
"Authorize.Net failure reason: %s \n"
"\n"
msgstr ""

#: gateway.php:503 gateway.php:616
#, php-format
msgid "Authorize.Net charge captured for %s (Charge ID: %s)."
msgstr ""

#: gateway.php:548 gateway.php:661
msgid "Unable to refund charge!"
msgstr ""

#: gateway.php:552 gateway.php:663
#, php-format
msgid "Authorize.Net charge refunded (Charge ID: %s)."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:36
#: includes/class-wc-authnet-privacy.php:24
#: includes/class-wc-gateway-authnet.php:36
msgid "Authorize.Net"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:37
#: includes/class-wc-gateway-authnet.php:37
#, php-format
msgid ""
"Live merchant accounts cannot be used in a sandbox environment, so to test "
"the plugin, please make sure you are using a separate sandbox account. If "
"you do not have a sandbox account, you can sign up for one from %shere%s."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:40
#: includes/class-wc-gateway-authnet.php:40
msgid "Upgrade to Enterprise"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:40
#: includes/class-wc-gateway-authnet.php:40
#, php-format
msgid ""
"Enterprise version is a full blown plugin that provides support for "
"<strong>processing subscriptions, pre-orders, one click upsells and payment "
"via saved cards or bank accounts</strong>. Some of these functionalities "
"require <strong>other premium plugins</strong> that are mentioned on the "
"page linked below. No card details or other sensitive data is stored on your "
"site.<br/><br/><a href=\"%s\" target=\"_blank\">Click here</a> to upgrade to "
"Enterprise version or to know more about it."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:67
#, php-format
msgid ""
"<br /><br /><strong>TEST MODE ENABLED</strong><br /> In test mode, you can "
"use the card number **************** with any CVC and a valid expiration "
"date or check the documentation \"<a href=\"%s\">%s API</a>\" for more card "
"numbers."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:117
#, php-format
msgid ""
"Authorize.Net error: Please enter your API Login ID <a href=\"%s\">here</a>"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:120
#, php-format
msgid ""
"Authorize.Net error: Please enter your Transaction Key <a href=\"%s\">here</"
"a>"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:126
#, php-format
msgid ""
"Authorize.Net is enabled, but a SSL certificate is not detected. Your "
"checkout may not be secure! Please ensure your server has a valid <a "
"href=\"%1$s\" target=\"_blank\">SSL certificate</a>"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:151
#: includes/class-wc-gateway-authnet.php:170
msgid "Enable/Disable"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:152
#: includes/class-wc-gateway-authnet.php:171
msgid "Enable Authorize.Net"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:158
#: includes/class-wc-gateway-authnet.php:177
msgid "Title"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:160
#: includes/class-wc-gateway-authnet.php:179
msgid "This controls the title which the user sees during checkout."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:161
msgid "Credit card"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:164
#: includes/class-wc-gateway-authnet.php:183
msgid "Description"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:166
#: includes/class-wc-gateway-authnet.php:185
msgid "This controls the description which the user sees during checkout."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:167
#: includes/class-wc-gateway-authnet.php:186
#, php-format
msgid "Pay with your credit card via %s."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:170
#: includes/class-wc-gateway-authnet.php:189
msgid "Sandbox mode"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:171
#: includes/class-wc-gateway-authnet.php:190
msgid "Enable Sandbox Mode"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:173
#: includes/class-wc-gateway-authnet.php:192
#, php-format
msgid ""
"Check the Authorize.Net testing guide %shere%s. This will display \"sandbox "
"mode\" warning on checkout."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:177
#: includes/class-wc-gateway-authnet.php:196
msgid "API Login ID"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:179
#: includes/class-wc-gateway-authnet.php:198
msgid ""
"Get it from Account → Security Settings → API Credentials & Keys page in "
"your Authorize.Net account."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:183
#: includes/class-wc-gateway-authnet.php:202
msgid "Transaction Key"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:185
#: includes/class-wc-gateway-authnet.php:204
msgid ""
"Get it from Account → Security Settings → API Credentials & Keys page in "
"your Authorize.Net account. For security reasons, you cannot view your "
"Transaction Key, but you will be able to generate a new one."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:189
#: includes/class-wc-gateway-authnet.php:208
msgid "Public Client Key"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:191
#: includes/class-wc-gateway-authnet.php:210
msgid ""
"Get it from Account → Security Settings → Manage Public Client Key page in "
"your Authorize.Net account."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:194
#: includes/class-wc-gateway-authnet.php:214
msgid "Statement Descriptor"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:196
#: includes/class-wc-gateway-authnet.php:216
msgid ""
"Extra information about a charge. This will appear in your order "
"description. Defaults to site name."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:201
#: includes/class-wc-gateway-authnet.php:221
msgid "Capture"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:202
#: includes/class-wc-gateway-authnet.php:222
msgid "Capture charge immediately"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:204
#: includes/class-wc-gateway-authnet.php:224
msgid ""
"Whether or not to immediately capture the charge. When unchecked, the charge "
"issues an authorization and will need to be captured later."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:209
#: includes/class-wc-gateway-authnet.php:229
msgid "Capture authorized transaction on status change"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:211
#: includes/class-wc-gateway-authnet.php:231
msgid ""
"Whether or not to capture the authorized transaction when you change the "
"order status from \"On Hold\" to \"Processing\" or \"Completed\". Disable if "
"you prefer to capture transactions in the gateway dashboard."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:215
#: includes/class-wc-gateway-authnet.php:235
msgid "Logging"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:216
#: includes/class-wc-gateway-authnet.php:236
msgid "Log debug messages"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:218
#: includes/class-wc-gateway-authnet.php:238
#, php-format
msgid ""
"Save debug messages to the WooCommerce System Status log file <code>%s</"
"code>."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:222
#: includes/class-wc-gateway-authnet.php:242
msgid "Gateway Debug"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:223
#: includes/class-wc-gateway-authnet.php:243
msgid "Log gateway requests and response to the WooCommerce System Status log."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:225
#: includes/class-wc-gateway-authnet.php:245
msgid ""
"<strong>CAUTION! Enabling this option will write gateway requests possibly "
"including card numbers and CVV to the logs.</strong> Do not turn this on "
"unless you have a problem processing credit cards. You must only ever enable "
"it temporarily for troubleshooting or to send requested information to the "
"plugin author. It must be disabled straight away after the issues are "
"resolved and the plugin logs should be deleted."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:225
#: includes/class-wc-gateway-authnet.php:245
#, php-format
msgid ""
"<a target=\"_blank\" href=\"%s\">Click here</a> to check and delete the full "
"log file."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:229
#: includes/class-wc-gateway-authnet.php:249
msgid "Line Items"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:230
#: includes/class-wc-gateway-authnet.php:250
msgid "Enable Line Items"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:232
#: includes/class-wc-gateway-authnet.php:252
msgid "Add line item data sent to the gateway."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:236
#: includes/class-wc-gateway-authnet.php:256
msgid "Allowed Card types"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:239
#: includes/class-wc-gateway-authnet.php:259
msgid "Select the card types you want to allow payments from."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:242
#: includes/class-wc-authnet-blocks-support.php:174
#: includes/class-wc-gateway-authnet.php:262
msgid "Visa"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:243
#: includes/class-wc-gateway-authnet.php:263
msgid "MasterCard"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:244
#: includes/class-wc-gateway-authnet.php:264
msgid "Discover"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:245
#: includes/class-wc-authnet-blocks-support.php:186
#: includes/class-wc-gateway-authnet.php:265
msgid "American Express"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:246
#: includes/class-wc-authnet-blocks-support.php:200
#: includes/class-wc-gateway-authnet.php:266
msgid "JCB"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:247
#: includes/class-wc-gateway-authnet.php:267
msgid "Diners Club"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:251
#: includes/class-wc-gateway-authnet.php:271
msgid "Receipt"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:252
#: includes/class-wc-gateway-authnet.php:272
msgid "Send Gateway Receipt"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:254
#: includes/class-wc-gateway-authnet.php:274
msgid ""
"If enabled, the customer will be sent an email receipt from Authorize.Net."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:258
#: includes/class-wc-gateway-authnet.php:278
msgid "Processing API"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:260
#: includes/class-wc-gateway-authnet.php:280
msgid "Always use \"Authorize.Net API\" unless you are using the AIM emulator."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:262
#: includes/class-wc-gateway-authnet.php:282
msgid "Authorize.Net API"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:263
#: includes/class-wc-gateway-authnet.php:283
msgid "Legacy AIM"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:286
#: includes/class-wc-gateway-authnet.php:328
msgid "Please accept the terms and conditions first"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:287
#: includes/class-wc-gateway-authnet.php:329
msgid "Please fill in required checkout fields first"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:288
#: includes/class-wc-gateway-authnet.php:330
msgid "Enter a card number."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:289
#: includes/class-wc-gateway-authnet.php:331
msgid "Enter an expiry date."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:290
#: includes/class-wc-gateway-authnet.php:332
#: includes/class-wc-gateway-authnet.php:538
msgid "CVC code is required."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:291
#: includes/class-wc-gateway-authnet.php:333
msgid "Invalid card number."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:292
#: includes/class-wc-gateway-authnet.php:334
msgid "Invalid card expiry date."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:293
#: includes/class-wc-gateway-authnet.php:335
msgid "Invalid card CVC."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:294
#: includes/class-wc-gateway-authnet.php:336
msgid "CVC"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:295
#: includes/class-wc-gateway-authnet.php:337
msgid "MM / YY"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:296
#: includes/class-wc-gateway-authnet.php:338
#: includes/class-wc-gateway-authnet.php:534
msgid "Card Type Not Accepted."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:325
msgid "Credit card details cannot be left incomplete."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:330
#: includes/class-wc-gateway-authnet.php:532
#, php-format
msgid ""
"Card type being used is not one of supported types in plugin settings: %s"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:331
msgid "Card Type Not Accepted"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:336
#, php-format
msgid "%s - Order %s"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:422
#: includes/class-wc-gateway-authnet.php:662
#, php-format
msgid ""
"Authorize.Net charge completed for %s (Charge ID: %s). \n"
"\n"
"AVS Response: %s \n"
"\n"
"CVV2 Response: %s"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:440
#: includes/class-wc-gateway-authnet.php:677
#, php-format
msgid ""
"Authorize.Net charge authorized for %s (Charge ID: %s). Process order to "
"take payment, or cancel to remove the pre-authorization.\n"
"\n"
"AVS Response: %s \n"
"\n"
"CVV2 Response: %s \n"
"\n"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:460
#: includes/aim/class-wc-gateway-authnet.php:461
#, php-format
msgid "Gateway Error: %s"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:464
#: includes/class-wc-gateway-authnet.php:620
#, php-format
msgid ""
"Authorize.Net failure reason: %s \n"
"\n"
"AVS Response: %s \n"
"\n"
"CVV2 Response: %s"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:531
#: includes/class-wc-gateway-authnet.php:771
#, php-format
msgid "Refunded %s - Refund ID: %s - Reason: %s"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:593
msgid "There was an error with the gateway response."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:612
#: includes/aim/class-wc-gateway-authnet.php:630
msgid "Your card has been declined."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:634
msgid "The credit card has expired."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:639
msgid "The merchant does not accept this type of credit card."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:643
msgid ""
"The address provided does not match the billing address of the cardholder. "
"Please verify the information and try again."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:647
msgid "The transaction amount is greater than the maximum amount allowed."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:660
msgid ""
"There was an error processing your credit card. Please verify the "
"information and try again."
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:828
#: includes/class-wc-gateway-authnet.php:932
msgid "Street Address: Match -- First 5 Digits of ZIP: No Match"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:829
#: includes/class-wc-gateway-authnet.php:933
msgid ""
"Address not provided for AVS check or street address match, postal code "
"could not be verified"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:830
#: includes/class-wc-gateway-authnet.php:934
msgid "AVS Error"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:831
#: includes/class-wc-gateway-authnet.php:935
msgid "Non U.S. Card Issuing Bank"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:832
#: includes/class-wc-gateway-authnet.php:936
msgid "Street Address: No Match -- First 5 Digits of ZIP: No Match"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:833
#: includes/class-wc-gateway-authnet.php:937
msgid "AVS not applicable for this transaction"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:834
#: includes/class-wc-gateway-authnet.php:938
msgid "Retry, System Is Unavailable"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:835
#: includes/class-wc-gateway-authnet.php:939
msgid "AVS Not Supported by Card Issuing Bank"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:836
#: includes/class-wc-gateway-authnet.php:940
msgid "Address Information For This Cardholder Is Unavailable"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:837
#: includes/class-wc-gateway-authnet.php:941
msgid "Street Address: No Match -- All 9 Digits of ZIP: Match"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:838
#: includes/class-wc-gateway-authnet.php:942
msgid "Street Address: Match -- All 9 Digits of ZIP: Match"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:839
#: includes/class-wc-gateway-authnet.php:943
msgid "Street Address: Match - First 5 Digits of ZIP: Match"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:840
#: includes/class-wc-gateway-authnet.php:944
msgid "Street Address: No Match - First 5 Digits of ZIP: Match"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:859
#: includes/class-wc-gateway-authnet.php:965
msgid "CVV2/CVC2 Match"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:860
#: includes/class-wc-gateway-authnet.php:966
msgid "CVV2 / CVC2 No Match"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:861
#: includes/class-wc-gateway-authnet.php:967
msgid "Not Processed"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:862
#: includes/class-wc-gateway-authnet.php:968
msgid "Merchant Has Indicated that CVV2 / CVC2 is not present on card"
msgstr ""

#: includes/aim/class-wc-gateway-authnet.php:863
#: includes/class-wc-gateway-authnet.php:969
msgid "Issuer is not certified and/or has not provided visa encryption keys"
msgstr ""

#: includes/class-wc-authnet-api.php:210
msgid "Unable to process request."
msgstr ""

#: includes/class-wc-authnet-blocks-support.php:161 src/index.js:30
msgid "Credit / Debit Card"
msgstr ""

#: includes/class-wc-authnet-blocks-support.php:180
msgid "Mastercard"
msgstr ""

#: includes/class-wc-authnet-blocks-support.php:194
msgctxt "Name of credit card"
msgid "Discover"
msgstr ""

#: includes/class-wc-authnet-blocks-support.php:206
msgid "Diners"
msgstr ""

#: includes/class-wc-authnet-privacy.php:26
msgid "WooCommerce Authorize.Net Order Data"
msgstr ""

#: includes/class-wc-authnet-privacy.php:27
msgid "WooCommerce Authorize.Net Data"
msgstr ""

#: includes/class-wc-authnet-privacy.php:38
msgid "Retain Authorize.Net Data"
msgstr ""

#: includes/class-wc-authnet-privacy.php:39
msgid ""
"Retains any Authorize.Net data such as Authorize.Net customer ID, charge ID."
msgstr ""

#: includes/class-wc-authnet-privacy.php:42
msgid "N/A"
msgstr ""

#: includes/class-wc-authnet-privacy.php:85
#, php-format
msgid ""
"By using this extension, you may be storing personal data or sharing data "
"with an external service. <a href=\"%s\" target=\"_blank\">Learn more about "
"how this works, including what you may want to include in your privacy "
"policy.</a>"
msgstr ""

#: includes/class-wc-authnet-privacy.php:108
msgid "Orders"
msgstr ""

#: includes/class-wc-authnet-privacy.php:111
msgid "Authorize.Net payment id"
msgstr ""

#: includes/class-wc-authnet-privacy.php:171
#, php-format
msgid ""
"Order ID %d is less than set retention days. Personal data retained. "
"(Authorize.Net)"
msgstr ""

#: includes/class-wc-authnet-privacy.php:181
msgid "Authorize.Net personal data erased."
msgstr ""

#: includes/class-wc-gateway-authnet.php:68
msgid "TEST MODE ENABLED"
msgstr ""

#: includes/class-wc-gateway-authnet.php:69
#, php-format
msgid ""
"In test mode, you can use the card number **************** with any CVC and "
"a valid expiration date or check the %sAuthorize.Net Testing Guide%s for "
"more card numbers and generate various test scenarios before going live."
msgstr ""

#: includes/class-wc-gateway-authnet.php:130
#, php-format
msgid "Gateway error: Please enter your API Login ID <a href=\"%s\">here</a>"
msgstr ""

#: includes/class-wc-gateway-authnet.php:135
#, php-format
msgid ""
"Gateway error: Please enter your Transaction Key <a href=\"%s\">here</a>"
msgstr ""

#: includes/class-wc-gateway-authnet.php:142
#, php-format
msgid ""
"Authorize.Net is enabled, but an SSL certificate is not detected. Your "
"checkout may not be secure! Please ensure your server has a valid <a "
"href=\"%1$s\" target=\"_blank\">SSL certificate</a>"
msgstr ""

#: includes/class-wc-gateway-authnet.php:180
msgid "Credit card (Authorize.Net)"
msgstr ""

#: includes/class-wc-gateway-authnet.php:456
#, php-format
msgid "%1$s - Order %2$s %3$s"
msgstr ""

#: includes/class-wc-gateway-authnet.php:580
msgid "Please enter your card details to make a payment."
msgstr ""

#: includes/class-wc-gateway-authnet.php:615
#, php-format
msgid "Error: %s"
msgstr ""

#: includes/class-wc-gateway-authnet.php:618
#, php-format
msgid "Authorize.Net failure reason: %s"
msgstr ""

#: includes/class-wc-gateway-authnet.php:632
#, php-format
msgid ""
"Payment method changed back to \"%1$s\" since the new card was not accepted."
msgstr ""

#: includes/class-wc-gateway-authnet.php:765
msgid "Gateway Error: "
msgstr ""

#: src/index.js:136
msgid "Authnet payment method"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "WooCommerce Authorize.Net Gateway"
msgstr ""

#. Plugin URI of the plugin/theme
msgid ""
"https://pledgedplugins.com/products/authorize-net-payment-gateway-"
"woocommerce/"
msgstr ""

#. Description of the plugin/theme
msgid ""
"A payment gateway for Authorize.Net. An Authorize.Net account and a server "
"with cURL, SSL support, and a valid SSL certificate is required (for "
"security reasons) for this gateway to function. Requires WC 3.3+"
msgstr ""

#. Author of the plugin/theme
msgid "Pledged Plugins"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://pledgedplugins.com"
msgstr ""
