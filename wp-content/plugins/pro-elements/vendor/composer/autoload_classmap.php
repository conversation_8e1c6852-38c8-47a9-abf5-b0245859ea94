<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'ElementorProDeps\\DI\\Annotation\\Inject' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Annotation/Inject.php',
    'ElementorProDeps\\DI\\Annotation\\Injectable' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Annotation/Injectable.php',
    'ElementorProDeps\\DI\\CompiledContainer' => $baseDir . '/vendor_prefixed/php-di/php-di/src/CompiledContainer.php',
    'ElementorProDeps\\DI\\Compiler\\Compiler' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Compiler/Compiler.php',
    'ElementorProDeps\\DI\\Compiler\\ObjectCreationCompiler' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Compiler/ObjectCreationCompiler.php',
    'ElementorProDeps\\DI\\Compiler\\RequestedEntryHolder' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Compiler/RequestedEntryHolder.php',
    'ElementorProDeps\\DI\\Container' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Container.php',
    'ElementorProDeps\\DI\\ContainerBuilder' => $baseDir . '/vendor_prefixed/php-di/php-di/src/ContainerBuilder.php',
    'ElementorProDeps\\DI\\Definition\\ArrayDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/ArrayDefinition.php',
    'ElementorProDeps\\DI\\Definition\\ArrayDefinitionExtension' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/ArrayDefinitionExtension.php',
    'ElementorProDeps\\DI\\Definition\\AutowireDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/AutowireDefinition.php',
    'ElementorProDeps\\DI\\Definition\\DecoratorDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/DecoratorDefinition.php',
    'ElementorProDeps\\DI\\Definition\\Definition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Definition.php',
    'ElementorProDeps\\DI\\Definition\\Dumper\\ObjectDefinitionDumper' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Dumper/ObjectDefinitionDumper.php',
    'ElementorProDeps\\DI\\Definition\\EnvironmentVariableDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/EnvironmentVariableDefinition.php',
    'ElementorProDeps\\DI\\Definition\\Exception\\InvalidAnnotation' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Exception/InvalidAnnotation.php',
    'ElementorProDeps\\DI\\Definition\\Exception\\InvalidDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Exception/InvalidDefinition.php',
    'ElementorProDeps\\DI\\Definition\\ExtendsPreviousDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/ExtendsPreviousDefinition.php',
    'ElementorProDeps\\DI\\Definition\\FactoryDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/FactoryDefinition.php',
    'ElementorProDeps\\DI\\Definition\\Helper\\AutowireDefinitionHelper' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Helper/AutowireDefinitionHelper.php',
    'ElementorProDeps\\DI\\Definition\\Helper\\CreateDefinitionHelper' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Helper/CreateDefinitionHelper.php',
    'ElementorProDeps\\DI\\Definition\\Helper\\DefinitionHelper' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Helper/DefinitionHelper.php',
    'ElementorProDeps\\DI\\Definition\\Helper\\FactoryDefinitionHelper' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Helper/FactoryDefinitionHelper.php',
    'ElementorProDeps\\DI\\Definition\\InstanceDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/InstanceDefinition.php',
    'ElementorProDeps\\DI\\Definition\\ObjectDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/ObjectDefinition.php',
    'ElementorProDeps\\DI\\Definition\\ObjectDefinition\\MethodInjection' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/ObjectDefinition/MethodInjection.php',
    'ElementorProDeps\\DI\\Definition\\ObjectDefinition\\PropertyInjection' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/ObjectDefinition/PropertyInjection.php',
    'ElementorProDeps\\DI\\Definition\\Reference' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Reference.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\ArrayResolver' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/ArrayResolver.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\DecoratorResolver' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/DecoratorResolver.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\DefinitionResolver' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/DefinitionResolver.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\EnvironmentVariableResolver' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/EnvironmentVariableResolver.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\FactoryResolver' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/FactoryResolver.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\InstanceInjector' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/InstanceInjector.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\ObjectCreator' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/ObjectCreator.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\ParameterResolver' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/ParameterResolver.php',
    'ElementorProDeps\\DI\\Definition\\Resolver\\ResolverDispatcher' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/ResolverDispatcher.php',
    'ElementorProDeps\\DI\\Definition\\SelfResolvingDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/SelfResolvingDefinition.php',
    'ElementorProDeps\\DI\\Definition\\Source\\AnnotationBasedAutowiring' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/AnnotationBasedAutowiring.php',
    'ElementorProDeps\\DI\\Definition\\Source\\Autowiring' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/Autowiring.php',
    'ElementorProDeps\\DI\\Definition\\Source\\DefinitionArray' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/DefinitionArray.php',
    'ElementorProDeps\\DI\\Definition\\Source\\DefinitionFile' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/DefinitionFile.php',
    'ElementorProDeps\\DI\\Definition\\Source\\DefinitionNormalizer' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/DefinitionNormalizer.php',
    'ElementorProDeps\\DI\\Definition\\Source\\DefinitionSource' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/DefinitionSource.php',
    'ElementorProDeps\\DI\\Definition\\Source\\MutableDefinitionSource' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/MutableDefinitionSource.php',
    'ElementorProDeps\\DI\\Definition\\Source\\NoAutowiring' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/NoAutowiring.php',
    'ElementorProDeps\\DI\\Definition\\Source\\ReflectionBasedAutowiring' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/ReflectionBasedAutowiring.php',
    'ElementorProDeps\\DI\\Definition\\Source\\SourceCache' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/SourceCache.php',
    'ElementorProDeps\\DI\\Definition\\Source\\SourceChain' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/Source/SourceChain.php',
    'ElementorProDeps\\DI\\Definition\\StringDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/StringDefinition.php',
    'ElementorProDeps\\DI\\Definition\\ValueDefinition' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Definition/ValueDefinition.php',
    'ElementorProDeps\\DI\\DependencyException' => $baseDir . '/vendor_prefixed/php-di/php-di/src/DependencyException.php',
    'ElementorProDeps\\DI\\FactoryInterface' => $baseDir . '/vendor_prefixed/php-di/php-di/src/FactoryInterface.php',
    'ElementorProDeps\\DI\\Factory\\RequestedEntry' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Factory/RequestedEntry.php',
    'ElementorProDeps\\DI\\Invoker\\DefinitionParameterResolver' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Invoker/DefinitionParameterResolver.php',
    'ElementorProDeps\\DI\\Invoker\\FactoryParameterResolver' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Invoker/FactoryParameterResolver.php',
    'ElementorProDeps\\DI\\NotFoundException' => $baseDir . '/vendor_prefixed/php-di/php-di/src/NotFoundException.php',
    'ElementorProDeps\\DI\\Proxy\\ProxyFactory' => $baseDir . '/vendor_prefixed/php-di/php-di/src/Proxy/ProxyFactory.php',
    'ElementorProDeps\\Invoker\\CallableResolver' => $baseDir . '/vendor_prefixed/php-di/invoker/src/CallableResolver.php',
    'ElementorProDeps\\Invoker\\Exception\\InvocationException' => $baseDir . '/vendor_prefixed/php-di/invoker/src/Exception/InvocationException.php',
    'ElementorProDeps\\Invoker\\Exception\\NotCallableException' => $baseDir . '/vendor_prefixed/php-di/invoker/src/Exception/NotCallableException.php',
    'ElementorProDeps\\Invoker\\Exception\\NotEnoughParametersException' => $baseDir . '/vendor_prefixed/php-di/invoker/src/Exception/NotEnoughParametersException.php',
    'ElementorProDeps\\Invoker\\Invoker' => $baseDir . '/vendor_prefixed/php-di/invoker/src/Invoker.php',
    'ElementorProDeps\\Invoker\\InvokerInterface' => $baseDir . '/vendor_prefixed/php-di/invoker/src/InvokerInterface.php',
    'ElementorProDeps\\Invoker\\ParameterResolver\\AssociativeArrayResolver' => $baseDir . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/AssociativeArrayResolver.php',
    'ElementorProDeps\\Invoker\\ParameterResolver\\Container\\ParameterNameContainerResolver' => $baseDir . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/Container/ParameterNameContainerResolver.php',
    'ElementorProDeps\\Invoker\\ParameterResolver\\Container\\TypeHintContainerResolver' => $baseDir . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/Container/TypeHintContainerResolver.php',
    'ElementorProDeps\\Invoker\\ParameterResolver\\DefaultValueResolver' => $baseDir . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/DefaultValueResolver.php',
    'ElementorProDeps\\Invoker\\ParameterResolver\\NumericArrayResolver' => $baseDir . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/NumericArrayResolver.php',
    'ElementorProDeps\\Invoker\\ParameterResolver\\ParameterResolver' => $baseDir . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/ParameterResolver.php',
    'ElementorProDeps\\Invoker\\ParameterResolver\\ResolverChain' => $baseDir . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/ResolverChain.php',
    'ElementorProDeps\\Invoker\\ParameterResolver\\TypeHintResolver' => $baseDir . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/TypeHintResolver.php',
    'ElementorProDeps\\Invoker\\Reflection\\CallableReflection' => $baseDir . '/vendor_prefixed/php-di/invoker/src/Reflection/CallableReflection.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Contracts\\Serializable' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Contracts/Serializable.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Contracts\\Signer' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Contracts/Signer.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Exceptions\\InvalidSignatureException' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Exceptions/InvalidSignatureException.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Exceptions\\MissingSecretKeyException' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Exceptions/MissingSecretKeyException.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Exceptions\\PhpVersionNotSupportedException' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Exceptions/PhpVersionNotSupportedException.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\SerializableClosure' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/SerializableClosure.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Serializers\\Native' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Serializers/Native.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Serializers\\Signed' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Serializers/Signed.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Signers\\Hmac' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Signers/Hmac.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Support\\ClosureScope' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Support/ClosureScope.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Support\\ClosureStream' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Support/ClosureStream.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Support\\ReflectionClosure' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Support/ReflectionClosure.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\Support\\SelfReference' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/Support/SelfReference.php',
    'ElementorProDeps\\Laravel\\SerializableClosure\\UnsignedSerializableClosure' => $baseDir . '/vendor_prefixed/laravel/serializable-closure/src/UnsignedSerializableClosure.php',
    'ElementorProDeps\\PhpDocReader\\AnnotationException' => $baseDir . '/vendor_prefixed/php-di/phpdoc-reader/src/PhpDocReader/AnnotationException.php',
    'ElementorProDeps\\PhpDocReader\\PhpDocReader' => $baseDir . '/vendor_prefixed/php-di/phpdoc-reader/src/PhpDocReader/PhpDocReader.php',
    'ElementorProDeps\\PhpDocReader\\PhpParser\\TokenParser' => $baseDir . '/vendor_prefixed/php-di/phpdoc-reader/src/PhpDocReader/PhpParser/TokenParser.php',
    'ElementorProDeps\\PhpDocReader\\PhpParser\\UseStatementParser' => $baseDir . '/vendor_prefixed/php-di/phpdoc-reader/src/PhpDocReader/PhpParser/UseStatementParser.php',
    'ElementorProDeps\\Psr\\Container\\ContainerExceptionInterface' => $baseDir . '/vendor_prefixed/psr/container/src/ContainerExceptionInterface.php',
    'ElementorProDeps\\Psr\\Container\\ContainerInterface' => $baseDir . '/vendor_prefixed/psr/container/src/ContainerInterface.php',
    'ElementorProDeps\\Psr\\Container\\NotFoundExceptionInterface' => $baseDir . '/vendor_prefixed/psr/container/src/NotFoundExceptionInterface.php',
);
