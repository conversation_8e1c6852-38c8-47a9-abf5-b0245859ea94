<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit4094f0d8dc8ec97601b04579d84317f5
{
    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'ElementorProDeps\\DI\\Annotation\\Inject' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Annotation/Inject.php',
        'ElementorProDeps\\DI\\Annotation\\Injectable' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Annotation/Injectable.php',
        'ElementorProDeps\\DI\\CompiledContainer' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/CompiledContainer.php',
        'ElementorProDeps\\DI\\Compiler\\Compiler' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Compiler/Compiler.php',
        'ElementorProDeps\\DI\\Compiler\\ObjectCreationCompiler' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Compiler/ObjectCreationCompiler.php',
        'ElementorProDeps\\DI\\Compiler\\RequestedEntryHolder' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Compiler/RequestedEntryHolder.php',
        'ElementorProDeps\\DI\\Container' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Container.php',
        'ElementorProDeps\\DI\\ContainerBuilder' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/ContainerBuilder.php',
        'ElementorProDeps\\DI\\Definition\\ArrayDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/ArrayDefinition.php',
        'ElementorProDeps\\DI\\Definition\\ArrayDefinitionExtension' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/ArrayDefinitionExtension.php',
        'ElementorProDeps\\DI\\Definition\\AutowireDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/AutowireDefinition.php',
        'ElementorProDeps\\DI\\Definition\\DecoratorDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/DecoratorDefinition.php',
        'ElementorProDeps\\DI\\Definition\\Definition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Definition.php',
        'ElementorProDeps\\DI\\Definition\\Dumper\\ObjectDefinitionDumper' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Dumper/ObjectDefinitionDumper.php',
        'ElementorProDeps\\DI\\Definition\\EnvironmentVariableDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/EnvironmentVariableDefinition.php',
        'ElementorProDeps\\DI\\Definition\\Exception\\InvalidAnnotation' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Exception/InvalidAnnotation.php',
        'ElementorProDeps\\DI\\Definition\\Exception\\InvalidDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Exception/InvalidDefinition.php',
        'ElementorProDeps\\DI\\Definition\\ExtendsPreviousDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/ExtendsPreviousDefinition.php',
        'ElementorProDeps\\DI\\Definition\\FactoryDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/FactoryDefinition.php',
        'ElementorProDeps\\DI\\Definition\\Helper\\AutowireDefinitionHelper' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Helper/AutowireDefinitionHelper.php',
        'ElementorProDeps\\DI\\Definition\\Helper\\CreateDefinitionHelper' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Helper/CreateDefinitionHelper.php',
        'ElementorProDeps\\DI\\Definition\\Helper\\DefinitionHelper' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Helper/DefinitionHelper.php',
        'ElementorProDeps\\DI\\Definition\\Helper\\FactoryDefinitionHelper' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Helper/FactoryDefinitionHelper.php',
        'ElementorProDeps\\DI\\Definition\\InstanceDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/InstanceDefinition.php',
        'ElementorProDeps\\DI\\Definition\\ObjectDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/ObjectDefinition.php',
        'ElementorProDeps\\DI\\Definition\\ObjectDefinition\\MethodInjection' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/ObjectDefinition/MethodInjection.php',
        'ElementorProDeps\\DI\\Definition\\ObjectDefinition\\PropertyInjection' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/ObjectDefinition/PropertyInjection.php',
        'ElementorProDeps\\DI\\Definition\\Reference' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Reference.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\ArrayResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/ArrayResolver.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\DecoratorResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/DecoratorResolver.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\DefinitionResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/DefinitionResolver.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\EnvironmentVariableResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/EnvironmentVariableResolver.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\FactoryResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/FactoryResolver.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\InstanceInjector' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/InstanceInjector.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\ObjectCreator' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/ObjectCreator.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\ParameterResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/ParameterResolver.php',
        'ElementorProDeps\\DI\\Definition\\Resolver\\ResolverDispatcher' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Resolver/ResolverDispatcher.php',
        'ElementorProDeps\\DI\\Definition\\SelfResolvingDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/SelfResolvingDefinition.php',
        'ElementorProDeps\\DI\\Definition\\Source\\AnnotationBasedAutowiring' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/AnnotationBasedAutowiring.php',
        'ElementorProDeps\\DI\\Definition\\Source\\Autowiring' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/Autowiring.php',
        'ElementorProDeps\\DI\\Definition\\Source\\DefinitionArray' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/DefinitionArray.php',
        'ElementorProDeps\\DI\\Definition\\Source\\DefinitionFile' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/DefinitionFile.php',
        'ElementorProDeps\\DI\\Definition\\Source\\DefinitionNormalizer' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/DefinitionNormalizer.php',
        'ElementorProDeps\\DI\\Definition\\Source\\DefinitionSource' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/DefinitionSource.php',
        'ElementorProDeps\\DI\\Definition\\Source\\MutableDefinitionSource' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/MutableDefinitionSource.php',
        'ElementorProDeps\\DI\\Definition\\Source\\NoAutowiring' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/NoAutowiring.php',
        'ElementorProDeps\\DI\\Definition\\Source\\ReflectionBasedAutowiring' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/ReflectionBasedAutowiring.php',
        'ElementorProDeps\\DI\\Definition\\Source\\SourceCache' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/SourceCache.php',
        'ElementorProDeps\\DI\\Definition\\Source\\SourceChain' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/Source/SourceChain.php',
        'ElementorProDeps\\DI\\Definition\\StringDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/StringDefinition.php',
        'ElementorProDeps\\DI\\Definition\\ValueDefinition' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Definition/ValueDefinition.php',
        'ElementorProDeps\\DI\\DependencyException' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/DependencyException.php',
        'ElementorProDeps\\DI\\FactoryInterface' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/FactoryInterface.php',
        'ElementorProDeps\\DI\\Factory\\RequestedEntry' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Factory/RequestedEntry.php',
        'ElementorProDeps\\DI\\Invoker\\DefinitionParameterResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Invoker/DefinitionParameterResolver.php',
        'ElementorProDeps\\DI\\Invoker\\FactoryParameterResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Invoker/FactoryParameterResolver.php',
        'ElementorProDeps\\DI\\NotFoundException' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/NotFoundException.php',
        'ElementorProDeps\\DI\\Proxy\\ProxyFactory' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/php-di/src/Proxy/ProxyFactory.php',
        'ElementorProDeps\\Invoker\\CallableResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/CallableResolver.php',
        'ElementorProDeps\\Invoker\\Exception\\InvocationException' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/Exception/InvocationException.php',
        'ElementorProDeps\\Invoker\\Exception\\NotCallableException' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/Exception/NotCallableException.php',
        'ElementorProDeps\\Invoker\\Exception\\NotEnoughParametersException' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/Exception/NotEnoughParametersException.php',
        'ElementorProDeps\\Invoker\\Invoker' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/Invoker.php',
        'ElementorProDeps\\Invoker\\InvokerInterface' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/InvokerInterface.php',
        'ElementorProDeps\\Invoker\\ParameterResolver\\AssociativeArrayResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/AssociativeArrayResolver.php',
        'ElementorProDeps\\Invoker\\ParameterResolver\\Container\\ParameterNameContainerResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/Container/ParameterNameContainerResolver.php',
        'ElementorProDeps\\Invoker\\ParameterResolver\\Container\\TypeHintContainerResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/Container/TypeHintContainerResolver.php',
        'ElementorProDeps\\Invoker\\ParameterResolver\\DefaultValueResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/DefaultValueResolver.php',
        'ElementorProDeps\\Invoker\\ParameterResolver\\NumericArrayResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/NumericArrayResolver.php',
        'ElementorProDeps\\Invoker\\ParameterResolver\\ParameterResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/ParameterResolver.php',
        'ElementorProDeps\\Invoker\\ParameterResolver\\ResolverChain' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/ResolverChain.php',
        'ElementorProDeps\\Invoker\\ParameterResolver\\TypeHintResolver' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/ParameterResolver/TypeHintResolver.php',
        'ElementorProDeps\\Invoker\\Reflection\\CallableReflection' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/invoker/src/Reflection/CallableReflection.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Contracts\\Serializable' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Contracts/Serializable.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Contracts\\Signer' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Contracts/Signer.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Exceptions\\InvalidSignatureException' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Exceptions/InvalidSignatureException.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Exceptions\\MissingSecretKeyException' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Exceptions/MissingSecretKeyException.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Exceptions\\PhpVersionNotSupportedException' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Exceptions/PhpVersionNotSupportedException.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\SerializableClosure' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/SerializableClosure.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Serializers\\Native' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Serializers/Native.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Serializers\\Signed' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Serializers/Signed.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Signers\\Hmac' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Signers/Hmac.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Support\\ClosureScope' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Support/ClosureScope.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Support\\ClosureStream' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Support/ClosureStream.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Support\\ReflectionClosure' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Support/ReflectionClosure.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\Support\\SelfReference' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/Support/SelfReference.php',
        'ElementorProDeps\\Laravel\\SerializableClosure\\UnsignedSerializableClosure' => __DIR__ . '/../..' . '/vendor_prefixed/laravel/serializable-closure/src/UnsignedSerializableClosure.php',
        'ElementorProDeps\\PhpDocReader\\AnnotationException' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/phpdoc-reader/src/PhpDocReader/AnnotationException.php',
        'ElementorProDeps\\PhpDocReader\\PhpDocReader' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/phpdoc-reader/src/PhpDocReader/PhpDocReader.php',
        'ElementorProDeps\\PhpDocReader\\PhpParser\\TokenParser' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/phpdoc-reader/src/PhpDocReader/PhpParser/TokenParser.php',
        'ElementorProDeps\\PhpDocReader\\PhpParser\\UseStatementParser' => __DIR__ . '/../..' . '/vendor_prefixed/php-di/phpdoc-reader/src/PhpDocReader/PhpParser/UseStatementParser.php',
        'ElementorProDeps\\Psr\\Container\\ContainerExceptionInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/container/src/ContainerExceptionInterface.php',
        'ElementorProDeps\\Psr\\Container\\ContainerInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/container/src/ContainerInterface.php',
        'ElementorProDeps\\Psr\\Container\\NotFoundExceptionInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/container/src/NotFoundExceptionInterface.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->classMap = ComposerStaticInit4094f0d8dc8ec97601b04579d84317f5::$classMap;

        }, null, ClassLoader::class);
    }
}
