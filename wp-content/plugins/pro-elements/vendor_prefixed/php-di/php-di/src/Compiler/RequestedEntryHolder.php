<?php

declare (strict_types=1);
namespace ElementorProDeps\DI\Compiler;

use ElementorProDeps\DI\Factory\RequestedEntry;
/**
 * <AUTHOR> <<EMAIL>>
 */
class RequestedEntryHolder implements RequestedEntry
{
    /**
     * @var string
     */
    private $name;
    public function __construct(string $name)
    {
        $this->name = $name;
    }
    public function getName() : string
    {
        return $this->name;
    }
}
