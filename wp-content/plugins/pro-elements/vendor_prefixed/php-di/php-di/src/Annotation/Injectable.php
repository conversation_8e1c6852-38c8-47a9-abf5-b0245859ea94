<?php

declare (strict_types=1);
namespace ElementorProDeps\DI\Annotation;

/**
 * "Injectable" annotation.
 *
 * Marks a class as injectable
 *
 * @api
 *
 * @Annotation
 * @Target("CLASS")
 *
 * <AUTHOR> Muskulus <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
final class Injectable
{
    /**
     * Should the object be lazy-loaded.
     * @var bool|null
     */
    private $lazy;
    public function __construct(array $values)
    {
        if (isset($values['lazy'])) {
            $this->lazy = (bool) $values['lazy'];
        }
    }
    /**
     * @return bool|null
     */
    public function isLazy()
    {
        return $this->lazy;
    }
}
