<?php

declare (strict_types=1);
namespace ElementorProDeps\DI\Definition\Source;

use ElementorProDeps\DI\Definition\Definition;
use ElementorProDeps\DI\Definition\Exception\InvalidDefinition;
/**
 * Source of definitions for entries of the container.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface DefinitionSource
{
    /**
     * Returns the DI definition for the entry name.
     *
     * @throws InvalidDefinition An invalid definition was found.
     * @return Definition|null
     */
    public function getDefinition(string $name);
    /**
     * @return Definition[] Definitions indexed by their name.
     */
    public function getDefinitions() : array;
}
