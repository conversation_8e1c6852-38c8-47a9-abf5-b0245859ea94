{"version": "0.4", "title": "Header 1", "type": "header", "metadata": {"template_type": "section-header", "include_in_zip": "1", "elementor_pro_required": "1", "elementor_library_type": "header", "elementor_pro_conditions": ["include/singular/page/6", "include/singular/page/462"], "additional_template_information": ["This is a \"Header\" template for Elementor Pro.", "This template will display on: Page #462."], "wp_page_template": "default"}, "content": [{"id": "5cc27005", "settings": {"html_tag": "header", "content_width": {"unit": "px", "size": 1760, "sizes": []}, "z_index": 10, "padding": {"unit": "px", "top": "0", "right": "44", "bottom": "0", "left": "44", "isLinked": false}, "padding_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "margin": {"unit": "px", "top": "0", "right": 0, "bottom": "-100", "left": 0, "isLinked": false}}, "elements": [{"id": "5d562b5", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "42eed464", "settings": {"structure": "30", "content_width": {"unit": "px", "size": 1260, "sizes": []}, "layout": "full_width"}, "elements": [{"id": "45cc2633", "settings": {"_column_size": 33, "_inline_size": 16, "_inline_size_tablet": 50, "_inline_size_mobile": 70, "content_position": "center"}, "elements": [{"id": "91dadca", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/01/<EMAIL>", "id": 720}, "space": {"unit": "px", "size": 144, "sizes": []}, "css_filters_brightness": {"unit": "px", "size": 121, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 0, "sizes": []}, "_element_width": "auto"}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "12451e51", "settings": {"_column_size": 33, "_inline_size": 53.333, "_inline_size_tablet": 50, "_inline_size_mobile": 30, "content_position": "center", "align_tablet": "flex-end"}, "elements": [{"id": "13073e9e", "settings": {"elementskit_nav_menu": "primary-menu", "elementskit_submenu_container_width": "220px", "elementskit_menubar_height": {"unit": "px", "size": 60, "sizes": []}, "elementskit_border_radius": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "elementskit_item_color_hover": "#36E3EE", "elementskit_style_tab_submenu_indicator_color": "#FFFFFF", "elementskit_submenu_item_spacing": {"unit": "px", "top": "10", "right": "15", "bottom": "10", "left": "15", "isLinked": false}, "elementskit_submenu_item_color": "#2D2D2D", "elementskit_menu_item_background_background": "classic", "elementskit_item_text_color_hover": "#36E3EE", "elementskit_nav_sub_menu_active_text_color": "#36E3EE", "elementskit_menu_item_border_border": "solid", "elementskit_menu_item_border_width": {"unit": "px", "top": "0", "right": "0", "bottom": "1", "left": "0", "isLinked": false}, "elementskit_menu_item_border_color": "#E2E8E7", "sub_panel_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "elementskit_panel_submenu_border_border": "solid", "elementskit_panel_submenu_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "elementskit_submenu_panel_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "elementskit_panel_box_shadow_box_shadow_type": "yes", "elementskit_panel_box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 0, "spread": 0, "color": "rgba(0, 0, 0, 0)"}, "_z_index": 0, "_element_width": "auto", "__globals__": {"elementskit_menu_text_color": "globals/colors?id=a2b3097", "elementskit_menu_item_typography_typography": "globals/typography?id=text", "elementskit_submenu_item_color": "globals/colors?id=text", "elementskit_style_tab_submenu_indicator_color": "", "elementskit_item_text_color_hover": "globals/colors?id=27bab1c", "elementskit_nav_sub_menu_active_text_color": "globals/colors?id=27bab1c", "elementskit_item_color_hover": "globals/colors?id=accent", "elementskit_nav_menu_active_text_color": "globals/colors?id=accent", "elementskit_menu_toggle_background_hover_color": "globals/colors?id=accent", "elementskit_menu_close_background_hover_color": "globals/colors?id=27bab1c", "elementskit_menu_close_border_color": "globals/colors?id=01a6083", "elementskit_menu_close_icon_color": "globals/colors?id=text", "elementskit_style_tab_submenu_indicator_color_tablet": "", "elementskit_item_text_color_hover_tablet": "globals/colors?id=27bab1c", "elementskit_nav_sub_menu_active_text_color_tablet": "globals/colors?id=27bab1c", "elementskit_item_color_hover_tablet": "globals/colors?id=27bab1c", "elementskit_nav_menu_active_text_color_tablet": "globals/colors?id=27bab1c", "elementskit_content_typography_typography": "globals/typography?id=text", "elementskit_panel_submenu_border_color": "globals/colors?id=01a6083", "elementskit_menu_toggle_background_color": "globals/colors?id=a2b3097"}, "elementskit_nav_menu_logo": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/<EMAIL>", "id": 722}, "elementskit_mobile_menu_panel_spacing_tablet": {"unit": "px", "top": "20", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "elementskit_mobile_menu_panel_width_tablet": {"unit": "px", "size": 320, "sizes": []}, "elementskit_nav_menu_active_text_color": "#36E3EE", "elementskit_menu_item_spacing_tablet": {"unit": "px", "top": "10", "right": "20", "bottom": "10", "left": "20", "isLinked": false}, "elementskit_style_tab_submenu_indicator_color_tablet": "#B8B8B8", "elementskit_submenu_item_spacing_tablet": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "elementskit_menu_toggle_spacing_tablet": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "elementskit_menu_toggle_width_tablet": {"unit": "px", "size": 46, "sizes": []}, "elementskit_menu_toggle_background_hover_background": "classic", "elementskit_menu_toggle_background_hover_color": "#36E3EE", "elementskit_menu_close_typography_font_size_tablet": {"unit": "px", "size": 12, "sizes": []}, "elementskit_menu_close_typography_font_weight": "600", "elementskit_menu_close_border_border": "solid", "elementskit_menu_close_background_hover_background": "classic", "elementskit_menu_close_background_hover_color": "#36E3EE", "elementskit_menu_close_border_hover_border": "solid", "elementskit_menu_close_border_hover_color": "#FFFFFF00", "elementskit_menu_close_icon_color_hover": "#FFFFFF80", "elementskit_mobile_menu_logo_padding_tablet": {"unit": "px", "top": "10", "right": "5", "bottom": "5", "left": "18", "isLinked": false}, "elementskit_menu_toggle_background_background": "classic", "elementskit_menu_item_border_last_child_border": "solid", "elementskit_menu_item_border_last_child_width": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "elementskit_menu_close_background_background": "classic", "elementskit_menu_close_background_color": "#FFFFFF", "elementskit_menu_item_background_hover_background": "classic", "elementskit_menu_item_background_hover_color": "#FFFFFF00", "elementskit_submenu_container_background_background": "classic", "elementskit_submenu_container_background_color": "#FFFFFF"}, "elements": [], "isInner": false, "widgetType": "ekit-nav-menu", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "********", "settings": {"_column_size": 33, "_inline_size": 30, "align": "flex-end", "z_index": 1, "__globals__": {"background_color": ""}, "content_position": "center", "hide_tablet": "hidden-tablet", "hide_mobile": "hidden-phone"}, "elements": [{"id": "6fd8263a", "settings": {"title": "My account", "link": {"url": "https://demo.deothemes.com/templatekit/pharmavo/my-account/", "is_external": "", "nofollow": "", "custom_attributes": ""}, "_margin": {"unit": "px", "top": "0", "right": "20", "bottom": "0", "left": "0", "isLinked": false}, "_element_width": "auto", "header_size": "span", "__globals__": {"typography_typography": "globals/typography?id=87b5c6e", "title_color": "globals/colors?id=a2b3097"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7e08581", "settings": {"ekit_search_placeholder_text": "Search...", "ekit_search_icons": {"value": "icon icon-search11", "library": "ekiticons"}, "ekit_search_icon_font_size": {"unit": "px", "size": 19, "sizes": []}, "ekit_searech_icon_color": "#FFFFFF", "ekit_search_margin": {"unit": "px", "top": "5", "right": "16", "bottom": "2", "left": "5", "isLinked": false}, "ekit_search_width": {"unit": "px", "size": 34, "sizes": []}, "ekit_search_height": {"unit": "px", "size": 34, "sizes": []}, "ekit_search_line_height": {"unit": "px", "size": 34, "sizes": []}, "_element_width": "auto"}, "elements": [], "isInner": false, "widgetType": "elementskit-header-search", "elType": "widget"}, {"id": "28d47217", "settings": {"icon": "bag-medium", "show_subtotal": "", "toggle_icon_size": {"unit": "px", "size": 24, "sizes": []}, "toggle_button_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "_element_width": "auto", "toggle_button_border_width": {"unit": "px", "size": 0, "sizes": []}, "__globals__": {"items_indicator_background_color": "globals/colors?id=accent", "toggle_button_icon_color": "globals/colors?id=a2b3097", "items_indicator_text_color": "globals/colors?id=primary", "product_title_color": "globals/colors?id=primary", "product_price_color": "globals/colors?id=ce2ffed", "divider_color": "globals/colors?id=01a6083"}, "toggle_button_hover_background_color": "#02010100", "button_border_radius": {"unit": "px", "size": 10, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "woocommerce-menu-cart", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}