{"version": "0.4", "title": "<PERSON><PERSON>, <PERSON><PERSON>, Account Page", "type": "single-page", "metadata": {"template_type": "single-page", "include_in_zip": "1", "elementor_pro_required": "1", "elementor_library_type": "single-page", "elementor_pro_conditions": ["include/singular/page/15", "include/singular/page/16", "include/singular/page/17"], "additional_template_information": ["This is a \"Single Page\" template for Elementor Pro.", "This template will display on: Page #17."], "wp_page_template": "default"}, "content": [{"id": "8cf4ef9", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "40", "right": "0", "bottom": "60", "left": "0", "isLinked": false}}, "elements": [{"id": "5080041e", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "4315fb4c", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "184ccc91", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "6695b3cd", "settings": {"__dynamic__": {"title": "[elementor-tag id=\"\" name=\"post-title\" settings=\"%7B%22before%22%3A%22%22%2C%22after%22%3A%22%22%2C%22fallback%22%3A%22%22%7D\"]"}, "title": "Add Your Heading Text Here", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=2e34d84"}}, "elements": [], "isInner": false, "widgetType": "theme-post-title", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "13705169", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "6d217015", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "bf485a7", "settings": [], "elements": [], "isInner": false, "widgetType": "theme-post-content", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}