{"version": "0.4", "title": "Page Home", "type": "section", "metadata": {"template_type": "single-home", "include_in_zip": "1", "elementor_pro_required": "1", "wp_page_template": "elementor_header_footer"}, "content": [{"id": "2612214f", "settings": {"background_background": "classic", "background_image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/hero_bg-min.jpg", "id": 615}, "background_overlay_background": "gradient", "background_overlay_color": "#03008E", "background_overlay_color_b": "#36E3EE", "background_overlay_gradient_angle": {"unit": "deg", "size": 130, "sizes": []}, "background_overlay_opacity": {"unit": "px", "size": 0.8, "sizes": []}, "content_width": {"unit": "px", "size": 1260, "sizes": []}, "background_attachment": "fixed", "background_repeat": "no-repeat", "background_size": "cover", "border_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "150", "isLinked": false}, "padding": {"unit": "px", "top": "140", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "margin": {"unit": "px", "top": "", "right": 0, "bottom": "", "left": 0, "isLinked": true}}, "elements": [{"id": "b823471", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "2e8c3597", "settings": {"layout": "full_width", "structure": "20"}, "elements": [{"id": "406c1d4a", "settings": {"_column_size": 50, "_inline_size": null, "padding": {"unit": "%", "top": "2", "right": "17", "bottom": "2", "left": "2", "isLinked": false}, "_inline_size_tablet": 100, "content_position": "center"}, "elements": [{"id": "33b998d5", "settings": {"title": "Your One Source", "header_size": "span", "__globals__": {"typography_typography": "globals/typography?id=6d30735", "title_color": "globals/colors?id=a2b3097"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "62a8a06f", "settings": {"title": "Maximize Sales<br>\nBest Medical Equipment", "header_size": "h1", "__globals__": {"title_color": "globals/colors?id=a2b3097", "typography_typography": "globals/typography?id=2e34d84"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "72ac73c7", "settings": {"text": "Browse Catalog", "size": "lg", "icon_indent": {"unit": "px", "size": 0, "sizes": []}, "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "700", "border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}, "_margin": {"unit": "px", "top": "20", "right": "0", "bottom": "70", "left": "0", "isLinked": false}, "__globals__": {"button_background_hover_color": "", "background_color": "", "hover_color": ""}, "button_background_hover_color": "#000000", "hover_color": "#FFFFFF"}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}, {"id": "766fc8b4", "settings": {"text": "Divider", "width": {"unit": "px", "size": 48, "sizes": []}, "align": "left", "weight": {"unit": "px", "size": 2, "sizes": []}, "_element_width": "auto", "__globals__": {"color": "globals/colors?id=01a6083"}}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}, {"id": "4ef15ca9", "settings": {"editor": "<p>Affordable for every hospital, clinic and medical practice to have the very best equipment, supplies and service.</p>", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "80", "isLinked": false}, "_element_width": "auto", "_margin": {"unit": "px", "top": "-28", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=9993f90", "text_color": "globals/colors?id=a2b3097"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "3a6bcec0", "settings": {"_column_size": 50, "_inline_size": null, "background_background": "classic", "__globals__": {"background_color": ""}, "_inline_size_tablet": 100, "content_position": "center", "margin_tablet": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "padding_tablet": {"unit": "px", "top": "60", "right": "20", "bottom": "60", "left": "20", "isLinked": false}, "padding_mobile": {"unit": "px", "top": "40", "right": "20", "bottom": "40", "left": "20", "isLinked": false}, "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-150", "left": "0", "isLinked": false}}, "elements": [{"id": "c698207", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/hero_img_front-min.jpg", "id": 621}, "image_border_radius": {"unit": "px", "top": "150", "right": "10", "bottom": "150", "left": "10", "isLinked": false}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": 50, "vertical": 50, "blur": 100, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.25)"}, "_z_index": 2, "_element_width": "auto", "_position": "absolute", "_offset_x": {"size": 1, "unit": "px"}, "_offset_y": {"size": 132, "unit": "px"}, "motion_fx_motion_fx_scrolling": "yes", "motion_fx_translateY_effect": "yes", "motion_fx_translateY_direction": "negative", "motion_fx_translateY_speed": {"unit": "px", "size": 0.5, "sizes": []}, "motion_fx_mouseTrack_effect": "yes"}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "74764016", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/hero_img_back-min.jpg", "id": 620}, "align": "right", "image_border_radius": {"unit": "px", "top": "10", "right": "150", "bottom": "10", "left": "150", "isLinked": false}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": 50, "vertical": 50, "blur": 100, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.25)"}, "_z_index": 1, "motion_fx_motion_fx_scrolling": "yes", "motion_fx_translateY_effect": "yes", "motion_fx_translateY_speed": {"unit": "px", "size": 2, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "2ae8f451", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "100", "right": "0", "bottom": "200", "left": "0", "isLinked": false}, "background_overlay_background": "classic", "background_overlay_image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/pattern_pills_right.png", "id": 781}, "background_overlay_position": "top right", "background_overlay_repeat": "no-repeat", "background_overlay_opacity": {"unit": "px", "size": 1, "sizes": []}, "background_background": "classic", "background_image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/pattern_pills_left.png", "id": 780}, "background_position": "top left", "background_repeat": "no-repeat", "padding_mobile": {"unit": "px", "top": "100", "right": "0", "bottom": "100", "left": "0", "isLinked": false}}, "elements": [{"id": "3bb8bb51", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "3a5ae1ad", "settings": {"padding": {"unit": "px", "top": "0", "right": "0", "bottom": "32", "left": "0", "isLinked": false}}, "elements": [{"id": "360bb862", "settings": {"_column_size": 100, "_inline_size": null, "space_between_widgets": 2, "align": "center"}, "elements": [{"id": "16e141a5", "settings": {"title": "Quality Products", "header_size": "span", "align": "center", "__globals__": {"title_color": "globals/colors?id=27bab1c", "typography_typography": "globals/typography?id=87b5c6e"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "5b0a0e14", "settings": {"title": "Browse Top Categories", "align": "center", "_margin": {"unit": "px", "top": "20", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=06deb8c"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "57ab9996", "settings": {"space": {"unit": "px", "size": 8, "sizes": []}, "_background_background": "gradient", "_background_color": "#36E3EE", "_background_gradient_angle": {"unit": "deg", "size": 270, "sizes": []}, "_border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}, "_element_width": "initial", "_element_custom_width": {"unit": "px", "size": 48, "sizes": []}, "__globals__": {"_background_color": "globals/colors?id=secondary", "_background_color_b": "globals/colors?id=c12169f"}}, "elements": [], "isInner": false, "widgetType": "spacer", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "4f9fa8c9", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "40"}, "elements": [{"id": "50f32536", "settings": {"_column_size": 25, "_inline_size": null, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}}, "elements": [{"id": "1132ca43", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/category_1-min.jpg", "id": 636}, "image_border_radius": {"unit": "px", "top": "200", "right": "200", "bottom": "200", "left": "200", "isLinked": true}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": 0, "vertical": 30, "blur": 50, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.15)"}, "link_to": "custom", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "hover_animation": "bob"}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "6ebd0db6", "settings": {"title": "Medical Accessories", "align": "center", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "header_size": "h4", "_margin": {"unit": "px", "top": "20", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "2e515c46", "settings": {"_column_size": 25, "_inline_size": null, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}}, "elements": [{"id": "15c93fb7", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/category_2-min.jpg", "id": 637}, "image_border_radius": {"unit": "px", "top": "200", "right": "200", "bottom": "200", "left": "200", "isLinked": true}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": 0, "vertical": 30, "blur": 50, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.15)"}, "link_to": "custom", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "hover_animation": "bob"}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "481c254d", "settings": {"title": "MRI Scanners", "align": "center", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "header_size": "h4", "_margin": {"unit": "px", "top": "20", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "6042fccb", "settings": {"_column_size": 25, "_inline_size": null, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}}, "elements": [{"id": "61dfbfaf", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/category_3-min.jpg", "id": 638}, "image_border_radius": {"unit": "px", "top": "200", "right": "200", "bottom": "200", "left": "200", "isLinked": true}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": 0, "vertical": 30, "blur": 50, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.15)"}, "link_to": "custom", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "hover_animation": "bob"}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "7f77f22f", "settings": {"title": "Medication", "align": "center", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "header_size": "h4", "_margin": {"unit": "px", "top": "20", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "18b93ede", "settings": {"_column_size": 25, "_inline_size": null}, "elements": [{"id": "445c781d", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/category_4-min.jpg", "id": 639}, "image_border_radius": {"unit": "px", "top": "200", "right": "200", "bottom": "200", "left": "200", "isLinked": true}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": 0, "vertical": 30, "blur": 50, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.15)"}, "link_to": "custom", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "hover_animation": "bob"}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "49518801", "settings": {"title": "Respiratory Ventilators", "align": "center", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "header_size": "h4", "_margin": {"unit": "px", "top": "20", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "41c2cd6f", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "background_background": "classic", "margin": {"unit": "px", "top": "", "right": 0, "bottom": "", "left": 0, "isLinked": true}, "padding": {"unit": "px", "top": "40", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=ccb22a5"}}, "elements": [{"id": "17631573", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "7f589a6c", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "layout": "full_width", "structure": "20"}, "elements": [{"id": "26f5c8bc", "settings": {"_column_size": 50, "_inline_size": 56, "background_background": "classic", "color_link": "#FFFFFF", "__globals__": {"background_color": "", "color_link_hover": "globals/colors?id=secondary"}, "border_radius": {"unit": "px", "top": "3", "right": "3", "bottom": "3", "left": "3", "isLinked": true}, "color_link_hover": "#36E3EE", "margin": {"unit": "px", "top": "-140", "right": "0", "bottom": "-140", "left": "0", "isLinked": false}, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}}, "elements": [{"id": "5f89674b", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/cta_promo-min.png", "id": 653}, "motion_fx_translateY_speed": {"unit": "px", "size": 1, "sizes": []}, "motion_fx_translateX_speed": {"unit": "px", "size": 1, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "4f85dc9a", "settings": {"_column_size": 50, "_inline_size": 44, "background_background": "classic", "__globals__": {"background_color": "", "color_link_hover": ""}, "border_radius": {"unit": "px", "top": "3", "right": "3", "bottom": "3", "left": "3", "isLinked": true}, "content_position": "center"}, "elements": [{"id": "219d8016", "settings": {"title": "25% OFF Protective Gears for Covid Protection\nDon’t miss it! use code PROTECT21", "header_size": "h3", "__globals__": {"typography_typography": "globals/typography?id=8cf6ce5"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "61a79195", "settings": {"text": "Shop Now", "size": "lg", "icon_indent": {"unit": "px", "size": 0, "sizes": []}, "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "700", "border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}, "__globals__": {"button_background_hover_color": "", "background_color": "", "hover_color": "", "button_text_color": ""}, "button_background_hover_color": "#000000", "hover_color": "#FFFFFF"}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "324f3371", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "100", "right": "0", "bottom": "0", "left": "0", "isLinked": false}}, "elements": [{"id": "13903fbc", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "690e8659", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "20"}, "elements": [{"id": "36027925", "settings": {"_column_size": 50, "_inline_size": null, "content_position": "center", "padding": {"unit": "%", "top": "5", "right": "20", "bottom": "5", "left": "5", "isLinked": false}, "padding_tablet": {"unit": "px", "top": "34", "right": "34", "bottom": "34", "left": "34", "isLinked": true}, "padding_mobile": {"unit": "px", "top": "48", "right": "10", "bottom": "10", "left": "10", "isLinked": false}}, "elements": [{"id": "6a51eb36", "settings": {"title": "The <PERSON> Begins", "header_size": "span", "__globals__": {"title_color": "globals/colors?id=27bab1c", "typography_typography": "globals/typography?id=87b5c6e"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "2c516129", "settings": {"title": "We envision a world where everyone can use the best health care services", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 28, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1.2, "sizes": []}, "__globals__": {"title_color": "globals/colors?id=primary"}, "typography_font_size_mobile": {"unit": "px", "size": 32, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3a62df5c", "settings": {"editor": "Synth ethical biodiesel poutine. Bitters beard salvia lo-fi. <PERSON><PERSON> santo literally plaid edison bulb, pitchfork drinking vinegar authentic pabst street art subway.", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "32", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=9993f90"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "742aefd", "settings": {"icon_list": [{"text": "Highly Motivated", "selected_icon": {"value": "far fa-check-circle", "library": "fa-regular"}, "_id": "5a9c815"}, {"text": "Friendly Personnel", "selected_icon": {"value": "far fa-check-circle", "library": "fa-regular"}, "_id": "e37dbb0"}, {"text": "15 Years of Excellence", "selected_icon": {"value": "far fa-check-circle", "library": "fa-regular"}, "_id": "c72a0ef"}], "space_between": {"unit": "px", "size": 10, "sizes": []}, "icon_color": "#58C256", "icon_size": {"unit": "px", "size": 24, "sizes": []}, "text_color": "#000000", "__globals__": {"text_color": "globals/colors?id=primary", "icon_typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "icon-list", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "28d20cb7", "settings": {"_column_size": 50, "_inline_size": null}, "elements": [{"id": "3bb8287c", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/promo_intro_1-min.jpg", "id": 661}, "align": "right", "image_border_radius": {"unit": "px", "top": "150", "right": "10", "bottom": "150", "left": "10", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "6e74b52b", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/promo_intro_2-min.jpg", "id": 660}, "align": "left", "image_border_radius": {"unit": "px", "top": "10", "right": "68", "bottom": "10", "left": "68", "isLinked": false}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": 50, "vertical": 50, "blur": 100, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.25)"}, "_margin_mobile": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "0", "isLinked": false}, "motion_fx_motion_fx_scrolling": "yes", "motion_fx_translateY_effect": "yes", "motion_fx_translateY_speed": {"unit": "px", "size": 3, "sizes": []}, "_element_width": "auto", "_element_width_tablet": "initial", "_element_custom_width_tablet": {"unit": "px", "size": 211}, "_position": "absolute", "_offset_x": {"size": -6, "unit": "px"}, "_offset_x_tablet": {"size": 122, "unit": "px"}, "_offset_x_mobile": {"size": 162, "unit": "px"}, "_offset_y": {"size": 163, "unit": "px"}, "_offset_y_tablet": {"size": 207, "unit": "px"}, "_offset_y_mobile": {"size": 197, "unit": "px"}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "698f8fa5", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "60", "right": "0", "bottom": "60", "left": "0", "isLinked": false}}, "elements": [{"id": "1036c9f2", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "33cb3cf9", "settings": {"padding": {"unit": "px", "top": "0", "right": "0", "bottom": "32", "left": "0", "isLinked": false}}, "elements": [{"id": "9aaf195", "settings": {"_column_size": 100, "_inline_size": null, "space_between_widgets": 2, "align": "center"}, "elements": [{"id": "64d16f35", "settings": {"title": "Best Selling Items", "align": "center", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=06deb8c"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7b4a72bd", "settings": {"space": {"unit": "px", "size": 8, "sizes": []}, "_background_background": "gradient", "_background_color": "#36E3EE", "_background_gradient_angle": {"unit": "deg", "size": 270, "sizes": []}, "_border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}, "_element_width": "initial", "_element_custom_width": {"unit": "px", "size": 48, "sizes": []}, "__globals__": {"_background_color": "globals/colors?id=secondary", "_background_color_b": "globals/colors?id=c12169f"}}, "elements": [], "isInner": false, "widgetType": "spacer", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "37c1a3da", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "6f9ec131", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "16685f21", "settings": {"columns": 5, "rows": 2, "align": "center", "image_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "image_spacing": {"unit": "px", "size": 14, "sizes": []}, "price_typography_typography": "custom", "price_typography_font_family": "Roboto", "price_typography_font_weight": "400", "old_price_typography_typography": "custom", "old_price_typography_font_family": "Roboto", "old_price_typography_font_weight": "400", "button_background_color": "#F6F3EA", "button_hover_color": "#FFFFFF", "show_onsale_flash": "", "__globals__": {"price_typography_typography": "globals/typography?id=text", "old_price_typography_typography": "globals/typography?id=text", "button_background_color": "globals/colors?id=bbf7ba1", "button_text_color": "globals/colors?id=primary", "button_hover_background_color": "globals/colors?id=primary", "star_color": "globals/colors?id=1fac42a", "empty_star_color": "globals/colors?id=1fac42a", "title_color": "globals/colors?id=primary", "title_typography_typography": "globals/typography?id=2b140c7", "old_price_color": "globals/colors?id=primary", "price_color": "globals/colors?id=ce2ffed", "button_typography_typography": "globals/typography?id=2ed7e3b", "view_cart_typography_typography": "globals/typography?id=2ed7e3b", "pagination_typography_typography": "globals/typography?id=text", "pagination_link_color": "globals/colors?id=primary", "pagination_link_bg_color_hover": "globals/colors?id=accent", "pagination_link_bg_color_active": "globals/colors?id=accent"}, "rating_spacing": {"unit": "px", "size": 14, "sizes": []}, "price_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "old_price_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "row_gap": {"unit": "px", "size": 48, "sizes": []}, "empty_star_color": "#FBBE47", "price_color": "#656565", "button_text_color": "#000000", "button_hover_background_color": "#000000", "button_border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}, "title_color": "#000000", "title_typography_typography": "custom", "title_typography_font_family": "<PERSON><PERSON>", "title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "title_typography_font_weight": "500", "star_color": "#FBBE47", "old_price_color": "#000000", "pagination_spacing": {"unit": "px", "size": 68, "sizes": []}, "show_pagination_border": "", "pagination_padding": {"unit": "px", "size": 16, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "woocommerce-products", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "32edf9a2", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "background_background": "gradient", "margin": {"unit": "px", "top": "168", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "padding": {"unit": "px", "top": "24", "right": "0", "bottom": "128", "left": "0", "isLinked": false}, "padding_tablet": {"unit": "px", "top": "0", "right": "0", "bottom": "80", "left": "0", "isLinked": false}, "padding_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "64", "left": "0", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=c12169f", "background_color_b": "globals/colors?id=secondary"}, "background_gradient_angle": {"unit": "deg", "size": 135, "sizes": []}}, "elements": [{"id": "b1fe9db", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "47929db1", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "20"}, "elements": [{"id": "686b9bc8", "settings": {"_column_size": 50, "_inline_size": 40, "margin": {"unit": "px", "top": "-178", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "motion_fx_motion_fx_scrolling": "yes", "motion_fx_translateY_effect": "yes", "motion_fx_translateY_speed": {"unit": "px", "size": 1, "sizes": []}}, "elements": [{"id": "1d7b60f0", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/testimonial_1-min.jpg", "id": 210}, "align": "left", "image_border_radius": {"unit": "px", "top": "150", "right": "10", "bottom": "150", "left": "10", "isLinked": false}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": 50, "vertical": 50, "blur": 100, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.25)"}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "44f3b904", "settings": {"_column_size": 50, "_inline_size": 60, "content_position": "center", "padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "70", "isLinked": false}, "padding_tablet": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "40", "isLinked": false}, "padding_mobile": {"unit": "px", "top": "52", "right": "10", "bottom": "10", "left": "10", "isLinked": false}, "animation": "fadeIn", "animation_delay": 200}, "elements": [{"id": "536fee14", "settings": {"selected_icon": {"value": "fas fa-quote-left", "library": "fa-solid"}, "align": "left", "primary_color": "#FFFFFF6B", "size": {"unit": "px", "size": 48, "sizes": []}, "_margin": {"unit": "px", "top": "24", "right": "0", "bottom": "0", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "icon", "elType": "widget"}, {"id": "25c03e06", "settings": {"editor": "Pandemic is not a word to use lightly or carelessly. It is not the time for countries to move toward mitigation, only if they are in a position to affect the course of the transmission of COVID-19. This could overwhelm a country's healthcare system.", "__globals__": {"text_color": "globals/colors?id=a2b3097", "typography_typography": "globals/typography?id=a62f5fb"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "27f95b8a", "settings": {"title": "by <PERSON><PERSON> ", "title_color": "#FFFFFF", "__globals__": {"title_color": "", "typography_typography": "globals/typography?id=87b5c6e"}, "header_size": "h4"}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "6e0c2e35", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "80", "right": "0", "bottom": "80", "left": "0", "isLinked": false}}, "elements": [{"id": "364565d7", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "7c5b000f", "settings": {"padding": {"unit": "px", "top": "0", "right": "0", "bottom": "32", "left": "0", "isLinked": false}}, "elements": [{"id": "66edac2a", "settings": {"_column_size": 100, "_inline_size": null, "space_between_widgets": 2, "align": "center"}, "elements": [{"id": "7e7a52f5", "settings": {"title": "From Our Blog", "align": "center", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=06deb8c"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7aabf459", "settings": {"space": {"unit": "px", "size": 8, "sizes": []}, "_background_background": "gradient", "_background_color": "#36E3EE", "_background_gradient_angle": {"unit": "deg", "size": 270, "sizes": []}, "_border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}, "_element_width": "initial", "_element_custom_width": {"unit": "px", "size": 48, "sizes": []}, "__globals__": {"_background_color": "globals/colors?id=secondary", "_background_color_b": "globals/colors?id=c12169f"}}, "elements": [], "isInner": false, "widgetType": "spacer", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "6925c63c", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "df59279", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "6cecc4c9", "settings": {"_skin": "cards", "classic_meta_separator": "/", "classic_show_read_more": "", "classic_read_more_text": "Read More »", "cards_thumbnail_size_size": "medium_large", "cards_item_ratio": {"unit": "px", "size": 0.7, "sizes": []}, "cards_meta_separator": "•", "cards_show_read_more": "", "cards_read_more_text": "Read More »", "cards_show_avatar": "", "full_content_meta_separator": "///", "classic_column_gap": {"unit": "px", "size": 20, "sizes": []}, "classic_img_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "classic_title_spacing": {"unit": "px", "size": 8, "sizes": []}, "classic_meta_spacing": {"unit": "px", "size": 9, "sizes": []}, "cards_card_border_radius": {"unit": "px", "size": 10, "sizes": []}, "cards_badge_size": {"unit": "px", "size": 12, "sizes": []}, "pagination_page_limit": "5", "pagination_prev_label": "&laquo; Previous", "pagination_next_label": "Next &raquo;", "__globals__": {"cards_excerpt_color": "globals/colors?id=text", "cards_badge_bg_color": "globals/colors?id=27bab1c", "cards_card_border_color": "globals/colors?id=01a6083", "cards_meta_border_color": "globals/colors?id=01a6083", "cards_badge_typography_typography": "globals/typography?id=secondary", "cards_title_color": "globals/colors?id=primary", "cards_meta_color": "globals/colors?id=text", "cards_excerpt_typography_typography": "globals/typography?id=text", "cards_title_typography_typography": "globals/typography?id=ac9324b", "cards_meta_typography_typography": "globals/typography?id=a9c604b"}, "cards_posts_per_page": 3, "cards_columns_tablet": "3", "pagination_spacing_top": {"unit": "px", "size": 48, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "posts", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}