{"version": "0.4", "title": "Products Archive", "type": "product-archive", "metadata": {"template_type": "archive-product", "include_in_zip": "1", "elementor_pro_required": "1", "elementor_library_type": "product-archive", "elementor_pro_conditions": ["include/product_archive"], "additional_template_information": ["This is a \"Product Archive\" template for Elementor Pro.", "This template will display on: All Product Archives."], "wp_page_template": "default"}, "content": [{"id": "29a866f", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "40", "right": "0", "bottom": "80", "left": "0", "isLinked": false}}, "elements": [{"id": "54facbe3", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "4b07d9e1", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "20", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "80", "left": "0", "isLinked": false}}, "elements": [{"id": "1ea6efa3", "settings": {"_column_size": 50, "_inline_size": null}, "elements": [{"id": "2b6b4b33", "settings": {"title": "Shop", "header_size": "h1", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=2e34d84"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "44cef56c", "settings": {"text": "Divider", "width": {"unit": "px", "size": 48, "sizes": []}, "align": "left", "weight": {"unit": "px", "size": 2, "sizes": []}, "_element_width": "auto", "__globals__": {"color": "globals/colors?id=01a6083"}}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}, {"id": "99693de", "settings": {"editor": "<p>Affordable for every hospital, clinic and medical practice to have the very best equipment, supplies and service.</p>", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "80", "isLinked": false}, "_element_width": "auto", "_margin": {"unit": "px", "top": "-28", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=9993f90"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "7c360752", "settings": {"_column_size": 50, "_inline_size": null}, "elements": [], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "7cd23f2a", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "45d08aa3", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "93392c4", "settings": {"row_gap": {"unit": "px", "size": 48, "sizes": []}, "align": "center", "image_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "image_spacing": {"unit": "px", "size": 14, "sizes": []}, "title_color": "#000000", "title_typography_typography": "custom", "title_typography_font_family": "<PERSON><PERSON>", "title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "title_typography_font_weight": "500", "star_color": "#FBBE47", "empty_star_color": "#FBBE47", "rating_spacing": {"unit": "px", "size": 14, "sizes": []}, "price_typography_typography": "custom", "price_typography_font_family": "Roboto", "price_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "price_typography_font_weight": "400", "old_price_typography_typography": "custom", "old_price_typography_font_family": "Roboto", "old_price_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "old_price_typography_font_weight": "400", "button_text_color": "#000000", "button_background_color": "#F6F3EA", "button_hover_color": "#FFFFFF", "button_hover_background_color": "#000000", "pagination_spacing": {"unit": "px", "size": 68, "sizes": []}, "show_pagination_border": "", "pagination_padding": {"unit": "px", "size": 16, "sizes": []}, "nothing_found_message": "It seems we can't find what you're looking for.", "__globals__": {"price_typography_typography": "globals/typography?id=text", "old_price_typography_typography": "globals/typography?id=text", "button_background_color": "globals/colors?id=bbf7ba1", "button_text_color": "globals/colors?id=primary", "button_hover_background_color": "globals/colors?id=primary", "star_color": "globals/colors?id=1fac42a", "empty_star_color": "globals/colors?id=1fac42a", "pagination_link_color": "globals/colors?id=text", "pagination_link_bg_color_active": "globals/colors?id=accent", "pagination_link_color_hover": "", "pagination_link_bg_color_hover": "globals/colors?id=accent", "pagination_border_color": "globals/colors?id=01a6083", "title_color": "globals/colors?id=primary", "title_typography_typography": "globals/typography?id=2b140c7", "old_price_color": "globals/colors?id=primary", "price_color": "globals/colors?id=ce2ffed", "button_typography_typography": "globals/typography?id=2ed7e3b", "view_cart_typography_typography": "globals/typography?id=2ed7e3b", "pagination_typography_typography": "globals/typography?id=text"}, "price_color": "#656565", "old_price_color": "#000000", "button_border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}, "show_onsale_flash": ""}, "elements": [], "isInner": false, "widgetType": "wc-archive-products", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}