{"version": "0.4", "title": "Page About Us", "type": "page", "metadata": {"template_type": "single-page", "include_in_zip": "1", "elementor_pro_required": "1", "wp_page_template": "elementor_header_footer"}, "content": [{"id": "1e67e8c", "settings": {"margin": {"unit": "px", "top": "", "right": 0, "bottom": "", "left": 0, "isLinked": true}, "content_width": {"unit": "px", "size": 1260, "sizes": []}, "background_background": "classic", "background_image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/about_page_title-min-1.jpg", "id": 693}, "background_repeat": "no-repeat", "background_size": "cover", "background_overlay_background": "gradient", "background_overlay_color": "#03008E", "background_overlay_color_b": "#36E3EE", "background_overlay_gradient_angle": {"unit": "deg", "size": 130, "sizes": []}, "background_overlay_opacity": {"unit": "px", "size": 0.8, "sizes": []}, "border_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "150", "isLinked": false}, "padding": {"unit": "px", "top": "140", "right": "0", "bottom": "40", "left": "0", "isLinked": false}}, "elements": [{"id": "50efec39", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "49efad15", "settings": {"layout": "full_width", "structure": "20"}, "elements": [{"id": "751b239e", "settings": {"_column_size": 50, "_inline_size": null, "padding": {"unit": "%", "top": "15", "right": "3", "bottom": "15", "left": "3", "isLinked": false}, "_inline_size_tablet": 100}, "elements": [{"id": "19267535", "settings": {"title": "About Us", "header_size": "h1", "__globals__": {"title_color": "globals/colors?id=a2b3097", "typography_typography": "globals/typography?id=2e34d84"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "73302c29", "settings": {"text": "Divider", "width": {"unit": "px", "size": 48, "sizes": []}, "align": "left", "weight": {"unit": "px", "size": 2, "sizes": []}, "_element_width": "auto", "__globals__": {"color": "globals/colors?id=01a6083"}}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}, {"id": "4a12f2e7", "settings": {"editor": "<p>Affordable for every hospital, clinic and medical practice to have the very best equipment, supplies and service.</p>", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "80", "isLinked": false}, "_element_width": "auto", "_margin": {"unit": "px", "top": "-28", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"text_color": "globals/colors?id=a2b3097", "typography_typography": "globals/typography?id=9993f90"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "c2f2022", "settings": {"_column_size": 50, "_inline_size": null, "__globals__": {"background_color": ""}, "_inline_size_tablet": 100, "content_position": "center", "margin_tablet": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "padding_tablet": {"unit": "px", "top": "60", "right": "20", "bottom": "60", "left": "20", "isLinked": false}, "padding_mobile": {"unit": "px", "top": "40", "right": "20", "bottom": "40", "left": "20", "isLinked": false}}, "elements": [], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "539da937", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "100", "right": "0", "bottom": "0", "left": "0", "isLinked": false}}, "elements": [{"id": "49eb8c7e", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "64fb1faa", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "20"}, "elements": [{"id": "6e94d279", "settings": {"_column_size": 50, "_inline_size": null}, "elements": [{"id": "7fba0ab0", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/about_intro_1-min.jpg", "id": 466}, "align": "left", "image_border_radius": {"unit": "px", "top": "10", "right": "150", "bottom": "10", "left": "150", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "15722077", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/about_intro_2-min.jpg", "id": 467}, "align": "left", "image_border_radius": {"unit": "px", "top": "10", "right": "68", "bottom": "10", "left": "68", "isLinked": false}, "image_box_shadow_box_shadow_type": "yes", "image_box_shadow_box_shadow": {"horizontal": -50, "vertical": 50, "blur": 100, "spread": 0, "color": "rgba(45, 50.00000000000001, 55, 0.25)"}, "_margin_mobile": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "0", "isLinked": false}, "motion_fx_motion_fx_scrolling": "yes", "motion_fx_translateY_effect": "yes", "motion_fx_translateY_speed": {"unit": "px", "size": 3, "sizes": []}, "_element_width": "auto", "_element_width_tablet": "initial", "_element_custom_width_tablet": {"unit": "px", "size": 211}, "_position": "absolute", "_offset_x": {"size": -6, "unit": "px"}, "_offset_x_tablet": {"size": 122, "unit": "px"}, "_offset_x_mobile": {"size": 162, "unit": "px"}, "_offset_y": {"size": 163, "unit": "px"}, "_offset_y_tablet": {"size": 207, "unit": "px"}, "_offset_y_mobile": {"size": 197, "unit": "px"}, "_offset_orientation_h": "end"}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "fdc852d", "settings": {"_column_size": 50, "_inline_size": null, "content_position": "center", "padding": {"unit": "%", "top": "5", "right": "5", "bottom": "5", "left": "20", "isLinked": false}, "padding_tablet": {"unit": "px", "top": "34", "right": "34", "bottom": "34", "left": "34", "isLinked": true}, "padding_mobile": {"unit": "px", "top": "48", "right": "10", "bottom": "10", "left": "10", "isLinked": false}}, "elements": [{"id": "7d13246f", "settings": {"title": "The <PERSON> Begins", "header_size": "span", "__globals__": {"title_color": "globals/colors?id=27bab1c", "typography_typography": "globals/typography?id=87b5c6e"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "44c1a646", "settings": {"title": "Our Story", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=06deb8c"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "1c65f15b", "settings": {"editor": "Synth ethical biodiesel poutine. Bitters beard salvia lo-fi. <PERSON><PERSON> santo literally plaid edison bulb, pitchfork drinking vinegar authentic pabst street art subway.", "__globals__": {"typography_typography": "globals/typography?id=9993f90"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "3a592ed8", "settings": {"editor": "<p>Synth ethical biodiesel poutine. Bitters beard salvia lo-fi. <PERSON><PERSON> santo literally plaid edison bulb, pitchfork drinking vinegar authentic pabst street art subway tile craft beer single-origin coffee shaman dreamcatcher.</p><p>Williamsburg sriracha portland, microdosing asymmetrical pork belly la croix 3 wolf moon umami.</p>", "typography_font_family": "Roboto", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "400"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "74f18539", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "60", "right": "0", "bottom": "0", "left": "0", "isLinked": false}}, "elements": [{"id": "49295f8c", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "4d6287e1", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "20"}, "elements": [{"id": "6300ba31", "settings": {"_column_size": 50, "_inline_size": 70}, "elements": [{"id": "39bd59ef", "settings": {"title": "Our Vision", "header_size": "span", "__globals__": {"title_color": "globals/colors?id=27bab1c", "typography_typography": "globals/typography?id=87b5c6e"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "251ee07c", "settings": {"title": "We envision a world where everyone can create powerful, flexible websites, and apps as easily as they create documents today.", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=06deb8c"}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "5c3742a1", "settings": {"_column_size": 50, "_inline_size": 30}, "elements": [], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "4d3f240d", "settings": {"content_width": {"unit": "px", "size": 1300, "sizes": []}}, "elements": [{"id": "4892e840", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "6b85f3e5", "settings": {"content_width": {"unit": "px", "size": 1300, "sizes": []}, "gap": "wider", "structure": "40"}, "elements": [{"id": "74fd6c22", "settings": {"_column_size": 25, "_inline_size": null, "_inline_size_tablet": 50, "margin_tablet": {"unit": "px", "top": "0", "right": "0", "bottom": "34", "left": "0", "isLinked": false}, "padding_tablet": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}}, "elements": [{"id": "1c65dbab", "settings": {"title": "01.", "header_size": "span", "title_color": "#0A3F36", "__globals__": {"title_color": "globals/colors?id=27bab1c", "typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "1cb348ae", "settings": {"title": "Customers First", "header_size": "h3", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=8cf6ce5"}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "558f9608", "settings": {"editor": "<p>Synth ethical biodiesel poutine. Bitters beard salvia lo-fi. <PERSON><PERSON> santo literally plaid edison, pitchfork drinking vinegar authentic pabst street art subway tile craft beer single-origin</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "7108e03d", "settings": {"_column_size": 25, "_inline_size": null, "_inline_size_tablet": 50, "margin_tablet": {"unit": "px", "top": "0", "right": "0", "bottom": "34", "left": "0", "isLinked": false}, "padding_tablet": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}}, "elements": [{"id": "7be2dee1", "settings": {"title": "02.", "header_size": "span", "title_color": "#0A3F36", "__globals__": {"title_color": "globals/colors?id=27bab1c", "typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3008f8cb", "settings": {"title": "Move Intentionally Fast", "header_size": "h3", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=8cf6ce5"}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "4e07e362", "settings": {"editor": "<p>Synth ethical biodiesel poutine. Bitters beard salvia lo-fi. <PERSON><PERSON> santo literally plaid edison, pitchfork drinking vinegar authentic pabst street art subway tile craft beer single-origin</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "3eeba1bc", "settings": {"_column_size": 25, "_inline_size": null, "_inline_size_tablet": 50, "margin_tablet": {"unit": "px", "top": "0", "right": "0", "bottom": "34", "left": "0", "isLinked": false}, "padding_tablet": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}}, "elements": [{"id": "225b00b3", "settings": {"title": "03.", "header_size": "span", "title_color": "#0A3F36", "__globals__": {"title_color": "globals/colors?id=27bab1c", "typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "4b87b881", "settings": {"title": "Lead By Serving Others", "header_size": "h3", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=8cf6ce5"}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "201dd06e", "settings": {"editor": "<p>Synth ethical biodiesel poutine. Bitters beard salvia lo-fi. <PERSON><PERSON> santo literally plaid edison, pitchfork drinking vinegar authentic pabst street art subway tile craft beer single-origin</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "48baeabd", "settings": {"_column_size": 25, "_inline_size": null, "_inline_size_tablet": 50, "padding_tablet": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}}, "elements": [{"id": "7fc7a761", "settings": {"title": "04.", "header_size": "span", "title_color": "#0A3F36", "__globals__": {"title_color": "globals/colors?id=27bab1c", "typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "743d9eee", "settings": {"title": "Think Big", "header_size": "h3", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=8cf6ce5"}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "60026837", "settings": {"editor": "<p>Synth ethical biodiesel poutine. Bitters beard salvia lo-fi. <PERSON><PERSON> santo literally plaid edison, pitchfork drinking vinegar authentic pabst street art subway tile craft beer single-origin</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "20b9c088", "settings": {"content_width": {"unit": "px", "size": 1330, "sizes": []}, "padding": {"unit": "px", "top": "20", "right": "0", "bottom": "60", "left": "0", "isLinked": false}}, "elements": [{"id": "3f347da3", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "2f139e5b", "settings": {"layout": "full_width", "content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "30"}, "elements": [{"id": "97b821e", "settings": {"_column_size": 33, "_inline_size": null, "space_between_widgets": 6, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "padding": {"unit": "px", "top": "44", "right": "44", "bottom": "44", "left": "44", "isLinked": true}, "padding_tablet": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "animation": "fadeIn", "animation_delay": 200}, "elements": [{"id": "96a8662", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/team_1-min.jpg", "id": 473}, "align": "left", "width": {"unit": "%", "size": 100, "sizes": []}, "space": {"unit": "%", "size": 100, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "76461883", "settings": {"title": "<PERSON>", "header_size": "h3", "__globals__": {"typography_typography": "globals/typography?id=8cf6ce5", "title_color": "globals/colors?id=primary"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "59310b8f", "settings": {"title": "CEO of Company", "header_size": "span", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "400", "__globals__": {"typography_typography": "globals/typography?id=text", "title_color": "globals/colors?id=text"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "b65e99a", "settings": {"_column_size": 33, "_inline_size": null, "space_between_widgets": 6, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "padding": {"unit": "px", "top": "44", "right": "44", "bottom": "44", "left": "44", "isLinked": true}, "padding_tablet": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "animation": "fadeIn", "animation_delay": 400}, "elements": [{"id": "551ef668", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/team_2-min.jpg", "id": 474}, "align": "left", "width": {"unit": "%", "size": 100, "sizes": []}, "space": {"unit": "%", "size": 100, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "7a5c26d7", "settings": {"title": "<PERSON><PERSON>", "header_size": "h3", "__globals__": {"typography_typography": "globals/typography?id=8cf6ce5", "title_color": "globals/colors?id=primary"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "5e3b5ae9", "settings": {"title": "Customers Support", "header_size": "span", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "400", "__globals__": {"typography_typography": "globals/typography?id=text", "title_color": "globals/colors?id=text"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "346587f5", "settings": {"_column_size": 33, "_inline_size": null, "space_between_widgets": 6, "padding": {"unit": "px", "top": "44", "right": "44", "bottom": "44", "left": "44", "isLinked": true}, "padding_tablet": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "animation": "fadeIn", "animation_delay": 600}, "elements": [{"id": "4cd73526", "settings": {"image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/team_3-min.jpg", "id": 475}, "align": "left", "width": {"unit": "%", "size": 100, "sizes": []}, "space": {"unit": "%", "size": 100, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "52fcfc60", "settings": {"title": "<PERSON><PERSON>", "header_size": "h3", "__globals__": {"typography_typography": "globals/typography?id=8cf6ce5", "title_color": "globals/colors?id=primary"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "62c3f3bf", "settings": {"title": "Sales", "header_size": "span", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "400", "__globals__": {"typography_typography": "globals/typography?id=text", "title_color": "globals/colors?id=text"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}