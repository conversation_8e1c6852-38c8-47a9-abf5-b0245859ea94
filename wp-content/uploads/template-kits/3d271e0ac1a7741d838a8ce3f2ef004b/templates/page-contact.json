{"version": "0.4", "title": "Page Contact", "type": "page", "metadata": {"template_type": "single-page", "include_in_zip": "1", "elementor_pro_required": "1", "wp_page_template": "elementor_header_footer"}, "content": [{"id": "7afb2186", "settings": {"layout": "full_width", "content_width": {"unit": "px", "size": 1260, "sizes": []}, "gap": "no"}, "elements": [{"id": "6e511db9", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "111a2c56", "settings": {"layout": "full_width", "content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "20"}, "elements": [{"id": "5ed55618", "settings": {"_column_size": 50, "_inline_size": null, "_inline_size_tablet": 100, "space_between_widgets": 8, "padding": {"unit": "%", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}}, "elements": [{"id": "7471387e", "settings": {"title": "We’d love to hear from you", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "32", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=06deb8c"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "2b0e5a6a", "settings": {"editor": "We are here to help you The Pharmavo Co. has been a valuable source of inspiration for natural medication and modern medical eupiment design.", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "29", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=6d30735"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "e15c980", "settings": {"title": "Address:", "header_size": "span", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=accent"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3d9c9367", "settings": {"editor": "Melbourne's GPO\n350 Bourke St\nMelbourne VIC 3000\nAustralia", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "59836033", "settings": {"title": "Call Us:", "header_size": "span", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=accent"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "19c3d30d", "settings": {"editor": "(821) 1554-456-123", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "1620be5e", "settings": {"title": "Email:", "header_size": "span", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=accent"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "52233e95", "settings": {"editor": "<p><a href=\"#\"><EMAIL></a></p>", "typography_font_family": "Roboto", "typography_font_weight": "400", "typography_text_decoration": "underline"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "38252546", "settings": {"_column_size": 50, "_inline_size": null, "_inline_size_tablet": 100, "background_background": "classic", "background_color": "#F5F6F8", "padding": {"unit": "%", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "__globals__": {"background_color": ""}}, "elements": [{"id": "1017f643", "settings": {"title": "Contact Us", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=8cf6ce5"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "4dc17a50", "settings": {"form_name": "Contact Form", "form_fields": [{"custom_id": "name", "field_label": "Name", "width": "50", "_id": "53fa0ad", "required": "true"}, {"custom_id": "email", "field_type": "email", "required": "true", "field_label": "Email", "width": "50", "_id": "9b15869"}, {"custom_id": "message", "field_type": "textarea", "field_label": "Message", "_id": "7a8be56", "required": "true", "rows": 7}], "input_size": "md", "mark_required": "yes", "button_size": "md", "button_align": "start", "step_next_label": "Next", "step_previous_label": "Previous", "button_text": "Send", "email_content": "[all-fields]", "email_content_2": "[all-fields]", "success_message": "The form was sent successfully.", "error_message": "An error occurred.", "required_field_message": "This field is required.", "invalid_message": "There's something wrong. The form is invalid.", "column_gap": {"unit": "px", "size": 20, "sizes": []}, "row_gap": {"unit": "px", "size": 20, "sizes": []}, "label_spacing": {"unit": "px", "size": 7, "sizes": []}, "field_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "__globals__": {"label_typography_typography": "globals/typography?id=87b5c6e", "button_background_hover_color": "globals/colors?id=primary", "previous_button_text_color": "globals/colors?id=primary", "button_text_color": "globals/colors?id=primary", "button_hover_color": "globals/colors?id=a2b3097", "previous_button_text_color_hover": "globals/colors?id=a2b3097", "previous_button_background_color_hover": "globals/colors?id=primary"}}, "elements": [], "isInner": false, "widgetType": "form", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "4008a53a", "settings": {"layout": "full_width", "gap": "no"}, "elements": [{"id": "6abad21", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "7e08e2ec", "settings": {"address": "London Eye, London, United Kingdom", "zoom": {"unit": "px", "size": 14, "sizes": []}, "height": {"unit": "px", "size": 460, "sizes": []}, "height_mobile": {"unit": "px", "size": 340, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "google_maps", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}