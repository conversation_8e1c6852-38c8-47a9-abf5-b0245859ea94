{"version": "0.4", "title": "Single Post", "type": "single-post", "metadata": {"template_type": "single-post", "include_in_zip": "1", "elementor_pro_required": "1", "elementor_library_type": "single-post", "elementor_pro_conditions": ["include/singular/post"], "additional_template_information": ["This is a \"Single Post\" template for Elementor Pro.", "This template will display on: Posts."], "wp_page_template": "default"}, "content": [{"id": "22c30fc0", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "56", "right": "0", "bottom": "0", "left": "0", "isLinked": false}}, "elements": [{"id": "42a65042", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "30b5a5d3", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "477ca77b", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "3aff242e", "settings": {"__dynamic__": {"title": "[elementor-tag id=\"\" name=\"post-title\" settings=\"%7B%22before%22%3A%22%22%2C%22after%22%3A%22%22%2C%22fallback%22%3A%22%22%7D\"]"}, "title": "Add Your Heading Text Here", "align": "center", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "32", "left": "0", "isLinked": false}, "typography_font_family": "<PERSON><PERSON>", "typography_font_weight": "600", "__globals__": {"title_color": "globals/colors?id=primary"}}, "elements": [], "isInner": false, "widgetType": "theme-post-title", "elType": "widget"}, {"id": "660d99ba", "settings": {"icon_list": [{"selected_icon": {"value": "fas fa-calendar", "library": "fa-solid"}, "_id": "cdaf9f1", "custom_date_format": "F j, Y", "custom_time_format": "g:i a"}, {"type": "author", "selected_icon": {"value": "far fa-user-circle", "library": "fa-regular"}, "_id": "dd32c5b", "custom_date_format": "F j, Y", "custom_time_format": "g:i a"}, {"type": "comments", "selected_icon": {"value": "far fa-comment-dots", "library": "fa-regular"}, "_id": "a8a1981", "custom_date_format": "F j, Y", "custom_time_format": "g:i a"}], "space_between": {"unit": "px", "size": 24, "sizes": []}, "icon_align": "center", "icon_size": {"unit": "px", "size": 13, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "__globals__": {"icon_color": "globals/colors?id=27bab1c", "text_color": "globals/colors?id=text", "icon_typography_typography": "globals/typography?id=a9c604b"}, "icon_color": "#3680EE", "text_color": "#2D2D2D"}, "elements": [], "isInner": false, "widgetType": "post-info", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "394cdd95", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "3d4f5a48", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "1f1742d7", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "margin": {"unit": "px", "top": "0", "right": 0, "bottom": "48", "left": 0, "isLinked": false}}, "elements": [{"id": "2840e33", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "19e0f74", "settings": {"__dynamic__": {"image": "[elementor-tag id=\"\" name=\"post-featured-image\" settings=\"%7B%22fallback%22%3A%7B%22url%22%3A%22%22%2C%22id%22%3A%22%22%7D%7D\"]"}, "image_size": "custom", "image_custom_dimension": {"width": "1240", "height": "608"}, "image_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}}, "elements": [], "isInner": false, "widgetType": "theme-post-featured-image", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "253fda91", "settings": {"content_width": {"unit": "px", "size": 680, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "80", "left": "0", "isLinked": false}}, "elements": [{"id": "7a16a822", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "3dfd54f0", "settings": [], "elements": [{"id": "42f0350e", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "d712344", "settings": {"__globals__": {"typography_typography": "globals/typography?id=9993f90"}}, "elements": [], "isInner": false, "widgetType": "theme-post-content", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "3541c60f", "settings": {"margin": {"unit": "px", "top": "10", "right": 0, "bottom": "40", "left": 0, "isLinked": false}}, "elements": [{"id": "7d8f4c03", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "3800609c", "settings": {"share_buttons": [{"_id": "5b7f28a"}, {"button": "twitter", "_id": "bdf3792"}, {"button": "linkedin", "_id": "cbd3c05"}], "skin": "boxed", "shape": "rounded", "column_gap": {"unit": "px", "size": 4, "sizes": []}, "button_size": {"unit": "px", "size": 1, "sizes": []}, "icon_size": {"unit": "em", "size": 1.5, "sizes": []}, "button_height": {"unit": "em", "size": 4, "sizes": []}, "border_size": {"unit": "px", "size": 1, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "share-buttons", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "2b950971", "settings": [], "elements": [{"id": "75969a98", "settings": {"_column_size": 100, "_inline_size": null, "background_background": "classic", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "40", "right": "40", "bottom": "30", "left": "40", "isLinked": false}, "__globals__": {"border_color": "globals/colors?id=01a6083", "background_color": "globals/colors?id=a0ca2a4"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}}, "elements": [{"id": "3b7be3fe", "settings": {"author_name": "<PERSON>", "author_bio": "Lorem ipsum dolor sit amet consectetur adipiscing elit dolor", "link_text": "All Posts", "image_size": {"unit": "px", "size": 46, "sizes": []}, "name_gap": {"unit": "px", "size": 15, "sizes": []}, "show_link": "", "__globals__": {"name_color": "globals/colors?id=primary", "name_typography_typography": "globals/typography?id=2b140c7"}}, "elements": [], "isInner": false, "widgetType": "author-box", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "665806f7", "settings": {"text": "Divider", "weight": {"unit": "px", "size": 2, "sizes": []}, "gap": {"unit": "px", "size": 40, "sizes": []}, "_padding": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "10", "isLinked": false}, "__globals__": {"color": "globals/colors?id=01a6083"}}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}, {"id": "33073caf", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "5a64181a", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "25359804", "settings": {"title": "Related Articles", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=8cf6ce5"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "1b44c529", "settings": {"classic_meta_separator": "/", "classic_show_read_more": "", "classic_read_more_text": "Read More »", "cards_thumbnail_size_size": "medium_large", "cards_item_ratio": {"unit": "px", "size": 0.7, "sizes": []}, "cards_meta_separator": "•", "cards_show_read_more": "", "cards_read_more_text": "Read More »", "cards_show_avatar": "", "full_content_meta_separator": "///", "classic_column_gap": {"unit": "px", "size": 20, "sizes": []}, "classic_img_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "classic_title_spacing": {"unit": "px", "size": 8, "sizes": []}, "classic_meta_spacing": {"unit": "px", "size": 9, "sizes": []}, "cards_card_border_color": "#E2E8E7", "cards_card_border_radius": {"unit": "px", "size": 10, "sizes": []}, "cards_meta_border_color": "#E2E8E7", "cards_badge_size": {"unit": "px", "size": 12, "sizes": []}, "cards_badge_typography_typography": "custom", "cards_badge_typography_font_family": "<PERSON><PERSON>", "cards_badge_typography_font_weight": "500", "cards_title_color": "#000000", "cards_title_typography_typography": "custom", "cards_title_typography_font_family": "<PERSON><PERSON>", "cards_title_typography_font_size": {"unit": "px", "size": 21, "sizes": []}, "cards_title_typography_font_weight": "600", "cards_meta_color": "#2D2D2D", "cards_meta_typography_typography": "custom", "cards_meta_typography_font_family": "<PERSON><PERSON>", "cards_meta_typography_font_size": {"unit": "px", "size": 13, "sizes": []}, "cards_meta_typography_font_weight": "300", "pagination_page_limit": "5", "pagination_prev_label": "&laquo; Previous", "pagination_next_label": "Next &raquo;", "__globals__": {"cards_excerpt_color": "globals/colors?id=text", "classic_title_color": "globals/colors?id=primary"}, "classic_posts_per_page": 3, "classic_show_excerpt": "", "classic_meta_data": [], "posts_post_type": "related", "classic_image_spacing": {"unit": "px", "size": 16, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "posts", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "30804d6f", "settings": {"text": "Divider", "weight": {"unit": "px", "size": 2, "sizes": []}, "gap": {"unit": "px", "size": 40, "sizes": []}, "_padding": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "10", "isLinked": false}, "__globals__": {"color": "globals/colors?id=01a6083"}}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}, {"id": "390b36f2", "settings": [], "elements": [{"id": "9424ae4", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "5be378a7", "settings": {"_skin": "theme_comments"}, "elements": [], "isInner": false, "widgetType": "post-comments", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}