{"version": "0.4", "title": "Page Blog", "type": "page", "metadata": {"template_type": "single-page", "include_in_zip": "1", "elementor_pro_required": "1", "wp_page_template": "elementor_header_footer"}, "content": [{"id": "16f5a9fa", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "padding": {"unit": "px", "top": "40", "right": "0", "bottom": "80", "left": "0", "isLinked": false}}, "elements": [{"id": "31bc4272", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "77d09af7", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "structure": "20", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "80", "left": "0", "isLinked": false}}, "elements": [{"id": "7f8f10d0", "settings": {"_column_size": 50, "_inline_size": null}, "elements": [{"id": "1f48b132", "settings": {"title": "Blog", "header_size": "h1", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=2e34d84"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "2fa576ca", "settings": {"text": "Divider", "width": {"unit": "px", "size": 48, "sizes": []}, "align": "left", "weight": {"unit": "px", "size": 2, "sizes": []}, "_element_width": "auto", "__globals__": {"color": "globals/colors?id=01a6083"}}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}, {"id": "22c67c11", "settings": {"editor": "<p>Affordable for every hospital, clinic and medical practice to have the very best equipment, supplies and service.</p>", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "80", "isLinked": false}, "_element_width": "auto", "_margin": {"unit": "px", "top": "-28", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"typography_typography": "globals/typography?id=9993f90"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "6e56cd2d", "settings": {"_column_size": 50, "_inline_size": null}, "elements": [], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "db078e1", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}}, "elements": [{"id": "4829c96e", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "31e0641", "settings": {"_skin": "cards", "classic_meta_separator": "/", "classic_show_read_more": "", "classic_read_more_text": "Read More »", "cards_thumbnail_size_size": "medium_large", "cards_item_ratio": {"unit": "px", "size": 0.7, "sizes": []}, "cards_meta_separator": "•", "cards_show_read_more": "", "cards_read_more_text": "Read More »", "cards_show_avatar": "", "full_content_meta_separator": "///", "classic_column_gap": {"unit": "px", "size": 20, "sizes": []}, "classic_img_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "classic_title_spacing": {"unit": "px", "size": 8, "sizes": []}, "classic_meta_spacing": {"unit": "px", "size": 9, "sizes": []}, "cards_card_border_radius": {"unit": "px", "size": 10, "sizes": []}, "cards_badge_size": {"unit": "px", "size": 12, "sizes": []}, "pagination_page_limit": "5", "pagination_prev_label": "&laquo; Previous", "pagination_next_label": "Next &raquo;", "__globals__": {"cards_excerpt_color": "globals/colors?id=text", "pagination_color": "globals/colors?id=text", "pagination_active_color": "globals/colors?id=27bab1c", "pagination_hover_color": "globals/colors?id=27bab1c", "cards_badge_bg_color": "globals/colors?id=27bab1c", "cards_card_border_color": "globals/colors?id=01a6083", "cards_meta_border_color": "globals/colors?id=01a6083", "cards_badge_typography_typography": "globals/typography?id=secondary", "cards_title_color": "globals/colors?id=primary", "cards_meta_color": "globals/colors?id=text", "cards_excerpt_typography_typography": "globals/typography?id=text", "cards_title_typography_typography": "globals/typography?id=ac9324b", "cards_meta_typography_typography": "globals/typography?id=a9c604b"}, "pagination_type": "numbers_and_prev_next", "pagination_spacing_top": {"unit": "px", "size": 48, "sizes": []}, "cards_columns_tablet": "3"}, "elements": [], "isInner": false, "widgetType": "posts", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}