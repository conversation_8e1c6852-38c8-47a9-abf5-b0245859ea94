{"version": "0.4", "title": "Global Kit Styles", "type": "section", "metadata": {"template_type": "global-styles", "include_in_zip": "1", "elementor_pro_required": null, "additional_template_information": ["These are the global theme styles configured through the Elementor Theme Styles area."], "wp_page_template": "default"}, "content": [], "page_settings": {"site_name": "TemplateKit | Pharmavo", "site_description": "Medical Accessories eCommerce Template Kit", "system_colors": [{"_id": "primary", "title": "Primary", "color": "#000000"}, {"_id": "secondary", "title": "Secondary", "color": "#36E3EE"}, {"_id": "text", "title": "Text", "color": "#2D2D2D"}, {"_id": "accent", "title": "Accent", "color": "#FFD66B"}], "custom_colors": [{"_id": "01a6083", "title": "Border", "color": "#E2E5E8"}, {"_id": "27bab1c", "title": "Main", "color": "#3680EE"}, {"_id": "ccb22a5", "title": "Light", "color": "#F4FFFE"}, {"_id": "c12169f", "title": "Dark", "color": "#03008E"}, {"_id": "a2b3097", "title": "White", "color": "#FFFFFF"}, {"_id": "1fac42a", "title": "Rating", "color": "#FBBE47"}, {"_id": "bbf7ba1", "title": "Beige", "color": "#F6F3EA"}, {"_id": "ce2ffed", "title": "Price", "color": "#656565"}, {"_id": "a0ca2a4", "title": "Light Grey", "color": "#F5F5F5"}], "system_typography": [{"_id": "primary", "title": "Primary", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_weight": "600"}, {"_id": "secondary", "title": "Secondary", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_weight": "400"}, {"_id": "text", "title": "Text", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_weight": "400", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}}, {"_id": "accent", "title": "Accent", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_weight": "700"}], "custom_typography": [{"_id": "9993f90", "title": "Lead Text", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_font_weight": "400"}, {"_id": "6d30735", "title": "Hero Subtitle", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_font_weight": "400", "typography_line_height": {"unit": "em", "size": 1.3, "sizes": []}}, {"_id": "87b5c6e", "title": "Section Subtitle", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 15, "sizes": []}, "typography_font_weight": "400"}, {"_id": "06deb8c", "title": "Section Title", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 37, "sizes": []}, "typography_font_weight": "600", "typography_font_size_tablet": {"unit": "px", "size": 34, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 32, "sizes": []}, "typography_line_height": {"unit": "em", "size": 1.2, "sizes": []}}, {"_id": "2e34d84", "title": "Hero Title", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 40, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 32, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1.2, "sizes": []}}, {"_id": "2b140c7", "title": "Category Title", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_font_weight": "500"}, {"_id": "8cf6ce5", "title": "CTA Title", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_font_weight": "600"}, {"_id": "2ed7e3b", "title": "<PERSON><PERSON> Small", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 14, "sizes": []}, "typography_font_weight": "700", "typography_line_height": {"unit": "em", "size": 1.2, "sizes": []}}, {"_id": "a62f5fb", "title": "Quote Text", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 21, "sizes": []}, "typography_font_weight": "300", "typography_line_height": {"unit": "em", "size": 1.3, "sizes": []}}, {"_id": "ac9324b", "title": "Post Card Title", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 21, "sizes": []}, "typography_font_weight": "600"}, {"_id": "a9c604b", "title": "Post Meta", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 14, "sizes": []}, "typography_font_weight": "300"}, {"_id": "c75ce54", "title": "Widget Title", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_font_weight": "600"}, {"_id": "4fc428d", "title": "Small Text", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 14, "sizes": []}}, {"_id": "312abae", "title": "404 Number", "typography_typography": "custom", "typography_font_family": "<PERSON><PERSON>", "typography_font_size": {"unit": "vw", "size": 10, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}}], "default_generic_fonts": "Sans-serif", "page_title_selector": "h1.entry-title", "activeItemIndex": 9, "button_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "__globals__": {"button_background_color": "globals/colors?id=accent", "button_typography_typography": "globals/typography?id=accent", "form_field_border_color": "globals/colors?id=01a6083", "button_hover_background_color": "globals/colors?id=primary", "link_normal_color": "globals/colors?id=27bab1c", "link_hover_color": "globals/colors?id=text", "button_text_color": "globals/colors?id=primary", "body_typography_typography": "globals/typography?id=text", "body_color": "globals/colors?id=text", "h1_color": "globals/colors?id=primary", "h2_color": "globals/colors?id=primary", "h3_color": "globals/colors?id=primary", "h4_color": "globals/colors?id=primary", "h5_color": "globals/colors?id=primary", "h6_color": "globals/colors?id=primary"}, "button_border_border": "solid", "button_border_width": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "form_field_border_border": "solid", "form_field_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "button_hover_text_color": "#FFFFFF", "link_hover_color": "#3680EE", "button_typography_typography": "custom", "button_typography_font_family": "<PERSON><PERSON>", "button_typography_font_weight": "700", "button_hover_background_color": "#000000", "form_field_border_color": "#E2E5E8", "button_background_color": "#FFD66B", "h1_typography_typography": "custom", "h1_typography_line_height": {"unit": "em", "size": 1.3, "sizes": []}, "h2_typography_typography": "custom", "h2_typography_line_height": {"unit": "em", "size": 1.3, "sizes": []}, "h3_typography_typography": "custom", "h3_typography_line_height": {"unit": "em", "size": 1.3, "sizes": []}, "h4_typography_typography": "custom", "h4_typography_line_height": {"unit": "em", "size": 1.3, "sizes": []}, "h5_typography_typography": "custom", "h5_typography_font_size": {"unit": "px", "size": 1.3, "sizes": []}, "h6_typography_typography": "custom", "h6_typography_line_height": {"unit": "em", "size": 1.3, "sizes": []}, "body_color": "#2D2D2D", "body_typography_typography": "custom", "body_typography_font_family": "Roboto", "body_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "body_typography_font_weight": "400", "button_text_color": "#000000", "button_hover_border_width": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}}