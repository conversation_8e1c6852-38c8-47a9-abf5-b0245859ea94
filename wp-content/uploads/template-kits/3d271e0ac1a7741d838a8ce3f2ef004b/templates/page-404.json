{"version": "0.4", "title": "Page 404", "type": "error-404", "metadata": {"template_type": "single-404", "include_in_zip": "1", "elementor_pro_required": "1", "elementor_library_type": "error-404", "elementor_pro_conditions": ["include/singular/not_found404"], "additional_template_information": ["This is a \"Error 404\" template for Elementor Pro.", "This template will display on: 404 Page."], "wp_page_template": "default"}, "content": [{"id": "24cd9729", "settings": {"layout": "full_width", "gap": "no"}, "elements": [{"id": "30b86a3f", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "170b98f6", "settings": {"layout": "full_width", "structure": "20"}, "elements": [{"id": "7f524d7", "settings": {"_column_size": 50, "_inline_size": null, "padding": {"unit": "%", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}}, "elements": [{"id": "2aac6b24", "settings": {"title": "404", "align": "center", "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=312abae"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "78cc229c", "settings": {"title": "Oops! Page Not Found", "header_size": "h3", "align": "center", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "28", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=primary", "typography_typography": "globals/typography?id=6d30735"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "374ba8c4", "settings": {"text": "Back to Home", "align": "center", "size": "lg", "icon_indent": {"unit": "px", "size": 0, "sizes": []}, "border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "7e3056d7", "settings": {"_column_size": 50, "_inline_size": null, "background_background": "classic", "__globals__": {"background_color": ""}, "background_image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/404_img-min.jpg", "id": 386}, "background_overlay_background": "classic", "background_overlay_color": "#1D242F", "background_overlay_opacity": {"unit": "px", "size": 0.42, "sizes": []}, "background_position": "center center", "background_repeat": "no-repeat", "background_size": "cover", "margin": {"unit": "px", "top": "-100", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}, "elements": [{"id": "4980d081", "settings": {"space": {"unit": "px", "size": 200, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "spacer", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}