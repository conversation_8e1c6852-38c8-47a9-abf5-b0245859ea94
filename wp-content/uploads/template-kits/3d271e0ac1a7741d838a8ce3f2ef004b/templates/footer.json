{"version": "0.4", "title": "Footer", "type": "footer", "metadata": {"template_type": "section-footer", "include_in_zip": "1", "elementor_pro_required": "1", "elementor_library_type": "footer", "elementor_pro_conditions": ["include/general"], "additional_template_information": ["This is a \"Footer\" template for Elementor Pro.", "This template will display on: Entire Site."], "wp_page_template": "default"}, "content": [{"id": "31354564", "settings": {"content_width": {"unit": "px", "size": 1300, "sizes": []}, "html_tag": "footer", "background_background": "classic", "background_image": {"url": "https://demo.deothemes.com/templatekit/pharmavo/wp-content/uploads/sites/7/2021/02/footer-min.jpg", "id": 607}, "background_position": "center center", "background_repeat": "no-repeat", "background_size": "cover", "background_overlay_background": "classic", "background_overlay_opacity": {"unit": "px", "size": 0.88, "sizes": []}, "padding": {"unit": "px", "top": "80", "right": "0", "bottom": "10", "left": "0", "isLinked": false}, "__globals__": {"background_overlay_color": "globals/colors?id=primary"}}, "elements": [{"id": "aff1ef3", "settings": {"_column_size": 100, "_inline_size": null, "space_between_widgets": 10}, "elements": [{"id": "4d949125", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "gap": "wider", "structure": "40", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}}, "elements": [{"id": "3a14a6c4", "settings": {"_column_size": 25, "_inline_size": null, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "_inline_size_tablet": 50}, "elements": [{"id": "465e45d2", "settings": {"title": "Company Info", "header_size": "h3", "__globals__": {"title_color": "globals/colors?id=a2b3097", "typography_typography": "globals/typography?id=c75ce54"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "5ccc8915", "settings": {"editor": "<p>Praesent varius augue urna, ut scelerisque augue lobortis eget. Vehicula enim id nom dapibus sem. Integer auctor, massa id rhonc ent varius augue urna.</p>", "__globals__": {"text_color": "globals/colors?id=a2b3097"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "72872a9", "settings": {"social_icon_list": [{"social_icon": {"value": "fab fa-facebook", "library": "fa-brands"}, "_id": "a319df3"}, {"social_icon": {"value": "fab fa-twitter", "library": "fa-brands"}, "_id": "3133699"}, {"social_icon": {"value": "fab fa-linkedin", "library": "fa-brands"}, "_id": "d5fa5ad"}, {"social_icon": {"value": "fab fa-youtube", "library": "fa-brands"}, "_id": "747355c"}], "align": "left", "icon_color": "custom", "icon_primary_color": "#FFFFFF00", "icon_secondary_color": "#FFD66B", "icon_size": {"unit": "px", "size": 18, "sizes": []}, "icon_padding": {"unit": "em", "size": 0, "sizes": []}, "icon_spacing": {"unit": "px", "size": 24, "sizes": []}, "__globals__": {"hover_primary_color": "", "hover_secondary_color": "globals/colors?id=a2b3097", "icon_primary_color": "", "icon_secondary_color": "globals/colors?id=accent"}}, "elements": [], "isInner": false, "widgetType": "social-icons", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "3791c137", "settings": {"_column_size": 25, "_inline_size": null, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "__globals__": {"color_link_hover": ""}, "_inline_size_tablet": 50}, "elements": [{"id": "6e6a216c", "settings": {"title": "Contact", "header_size": "h3", "__globals__": {"title_color": "globals/colors?id=a2b3097", "typography_typography": "globals/typography?id=c75ce54"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "61b43c53", "settings": {"icon_list": [{"text": "1-677-124-44227", "selected_icon": {"value": "fas fa-phone-alt", "library": "fa-solid"}, "_id": "e5541fd", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}}, {"text": "No: 58 A, East Madison Street, Baltimore MD, USA 4508", "selected_icon": {"value": "fas fa-map-marker-alt", "library": "fa-solid"}, "_id": "c27c5f0"}, {"text": "Mon - Sun 8.00 - 18.00", "selected_icon": {"value": "fas fa-clock", "library": "fa-solid"}, "_id": "aeebb11"}], "space_between": {"unit": "px", "size": 16, "sizes": []}, "icon_color": "#FFD66B", "text_color": "#FFFFFF", "icon_color_hover": "#FFFFFF", "icon_size": {"unit": "px", "size": 16, "sizes": []}, "__globals__": {"icon_color": "globals/colors?id=accent", "text_color_hover": ""}}, "elements": [], "isInner": false, "widgetType": "icon-list", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "373de14a", "settings": {"_column_size": 25, "_inline_size": null, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "_inline_size_tablet": 50}, "elements": [{"id": "56fb3d24", "settings": {"title": "Useful Links", "header_size": "h3", "title_color": "#FFFFFF", "__globals__": {"typography_typography": "globals/typography?id=c75ce54"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "5835ea44", "settings": {"icon_list": [{"text": "About Us", "selected_icon": {"value": "fas fa-chevron-right", "library": "fa-solid"}, "_id": "2f248c2", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}}, {"text": "Contact", "selected_icon": {"value": "fas fa-chevron-right", "library": "fa-solid"}, "_id": "a0910f0", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}}, {"text": "How We Work", "selected_icon": {"value": "fas fa-chevron-right", "library": "fa-solid"}, "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "_id": "adc0fd7"}, {"text": "Recent News", "selected_icon": {"value": "fas fa-chevron-right", "library": "fa-solid"}, "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}, "_id": "9c8952a"}], "space_between": {"unit": "px", "size": 8, "sizes": []}, "text_color": "#FFFFFF", "__globals__": {"icon_color": "globals/colors?id=accent", "text_color_hover": "globals/colors?id=accent"}}, "elements": [], "isInner": false, "widgetType": "icon-list", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "48079573", "settings": {"_column_size": 25, "_inline_size": null, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "_inline_size_tablet": 50}, "elements": [{"id": "5ad94918", "settings": {"title": "Newsletter", "header_size": "h3", "title_color": "#FFFFFF", "__globals__": {"typography_typography": "globals/typography?id=c75ce54"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "54de2b07", "settings": {"editor": "<p>Stay in touch for the latest updates. No spam, we promise.</p>", "__globals__": {"text_color": "globals/colors?id=a2b3097"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "40e3a3e3", "settings": {"ekit_mail_chimp_section_form_name_show": "", "ekit_mail_chimp_email_address_placeholder": "Your email", "ekit_mail_chimp_email_icon_show": "", "ekit_mail_chimp_submit": "", "ekit_mail_chimp_submit_icons": {"value": "fas fa-location-arrow", "library": "fa-solid"}, "ekit_mail_chimp_success_message": "Successfully listed this email", "ekit_mail_chimp_input_style_padding": {"unit": "px", "top": "14", "right": "20", "bottom": "14", "left": "20", "isLinked": false}, "ekit_mail_chimp_input_style_width__switch": "yes", "ekit_mail_chimp_input_style_width": {"unit": "%", "size": 74, "sizes": []}, "ekit_mail_chimp_input_style_margin_right": {"unit": "px", "size": "", "sizes": []}, "ekit_mail_chimp_button_border_padding": {"unit": "px", "top": "8", "right": "20", "bottom": "8", "left": "20", "isLinked": false}, "ekit_mail_chimp_button_style_use_width_height": "yes", "ekit_mail_chimp_button_width": {"unit": "px", "size": 85, "sizes": []}, "ekit_mail_chimp_button_style_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "6", "left": "-20", "isLinked": false}, "ekit_mail_chimp_button_background_background": "classic", "ekit_mail_chimp_button_background_hover_background": "classic", "ekit_mail_chimp_button_icon_padding_right": {"unit": "px", "size": "", "sizes": []}, "__globals__": {"ekit_mail_chimp_button_color": "globals/colors?id=primary", "ekit_mail_chimp_button_background_color": "globals/colors?id=accent", "ekit_mail_chimp_button_color_hover": "globals/colors?id=accent", "ekit_mail_chimp_input_label_color": "globals/colors?id=primary", "ekit_mail_chimp_input_style_placeholder_color": "globals/colors?id=text", "ekit_mail_chimp_button_background_hover_color": "globals/colors?id=primary", "ekit_mail_chimp_input_icon_color": "globals/colors?id=primary"}}, "elements": [], "isInner": false, "widgetType": "elementskit-mail-chimp", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "1dc9785b", "settings": {"text": "Divider", "color": "#FFFFFF38", "gap": {"unit": "px", "size": 6, "sizes": []}, "_padding": {"unit": "px", "top": "0", "right": "40", "bottom": "0", "left": "40", "isLinked": false}, "_padding_mobile": {"unit": "px", "top": "0", "right": "30", "bottom": "0", "left": "30", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}, {"id": "3aca6f2", "settings": {"content_width": {"unit": "px", "size": 1260, "sizes": []}, "gap": "wider", "structure": "20", "margin": {"unit": "px", "top": "-12", "right": 0, "bottom": "-20", "left": 0, "isLinked": false}}, "elements": [{"id": "6bb8d972", "settings": {"_column_size": 50, "_inline_size": null, "space_between_widgets": 0}, "elements": [{"id": "2edf3a68", "settings": {"editor": "<p>Copyright © 2021 Pharmavo, Made by DeoThemes.</p>", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-8", "left": "0", "isLinked": false}, "__globals__": {"text_color": "globals/colors?id=a2b3097", "typography_typography": "globals/typography?id=4fc428d"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "3115e70d", "settings": {"view": "inline", "icon_list": [{"text": "Privacy Policy", "selected_icon": {"value": "", "library": ""}, "_id": "cd53aa5", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}}, {"text": "Terms & Conditions", "selected_icon": {"value": "", "library": ""}, "_id": "36d8f8a", "link": {"url": "#", "is_external": "", "nofollow": "", "custom_attributes": ""}}], "text_color": "#FFFFFF", "__globals__": {"text_color_hover": "globals/colors?id=accent", "icon_typography_typography": "globals/typography?id=4fc428d"}}, "elements": [], "isInner": false, "widgetType": "icon-list", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "5b60b341", "settings": {"_column_size": 50, "_inline_size": null, "content_position": "center", "align": "flex-end", "align_mobile": "flex-start"}, "elements": [{"id": "37065dc4", "settings": {"view": "inline", "icon_list": [{"text": "", "selected_icon": {"value": "fab fa-cc-paypal", "library": "fa-brands"}, "_id": "2082ec7"}, {"text": "", "selected_icon": {"value": "fab fa-cc-visa", "library": "fa-brands"}, "_id": "446583a"}, {"text": "", "selected_icon": {"value": "fab fa-cc-mastercard", "library": "fa-brands"}, "_id": "ed2ab29"}, {"text": "", "selected_icon": {"value": "fab fa-cc-amex", "library": "fa-brands"}, "_id": "ee18c60"}], "space_between": {"unit": "px", "size": 2, "sizes": []}, "icon_align": "right", "icon_color": "#FFFFFF82", "icon_size": {"unit": "px", "size": 38, "sizes": []}, "__globals__": {"text_color": "globals/colors?id=text"}, "icon_align_mobile": "left"}, "elements": [], "isInner": false, "widgetType": "icon-list", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}]}