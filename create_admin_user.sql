-- Create a new WordPress admin user
-- Change 'newadmin', '<EMAIL>', and 'password123' to your preferences

INSERT INTO wp_users (user_login, user_pass, user_nicename, user_email, user_registered, user_activation_key, user_status, display_name) 
VALUES ('newadmin', MD5('password123'), 'newadmin', '<EMAIL>', NOW(), '', 0, 'New Admin');

-- Get the user ID for the capabilities
SET @user_id = LAST_INSERT_ID();

-- Add admin capabilities
INSERT INTO wp_usermeta (user_id, meta_key, meta_value) 
VALUES (@user_id, 'wp_capabilities', 'a:1:{s:13:"administrator";b:1;}');

INSERT INTO wp_usermeta (user_id, meta_key, meta_value) 
VALUES (@user_id, 'wp_user_level', '10');