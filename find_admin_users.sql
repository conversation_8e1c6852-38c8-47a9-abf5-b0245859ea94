-- Find WordPress admin users and their login details
SELECT user_login, user_email, user_nicename, user_status 
FROM wp_users 
WHERE user_status = 0
ORDER BY ID;

-- Show user capabilities to identify admin users
SELECT u.user_login, u.user_email, um.meta_value as capabilities
FROM wp_users u
JOIN wp_usermeta um ON u.ID = um.user_id
WHERE um.meta_key = 'wp_capabilities'
AND um.meta_value LIKE '%administrator%';